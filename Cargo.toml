# The curve25519-dalek crate uses the `simd` backend by default in v4 if
# possible, which has very slow performance on some platforms with opt-level 0,
# which is the default for `dev` and `test` builds. This slowdown causes
# certain interactions in the solana-test-validator, such as verifying ZK
# proofs in transactions, to take much more than 400ms, creating problems in
# the test environment. To give better performance in the solana-test-validator
# during tests and dev builds, override the opt-level to 3 for the crate.
[profile.dev.package.curve25519-dalek]
opt-level = 3

[workspace]
resolver = "2"
members = [
  "clients/cli",
  #"clients/rust", omitted from workspace until a real client is generated
  "clients/rust-legacy",
  "confidential/ciphertext-arithmetic",
  "confidential/elgamal-registry",
  "confidential/proof-extraction",
  "confidential/proof-generation",
  "confidential/proof-tests",
  "interface",
  "program",
]

[workspace.package]
authors = ["Anza Maintainers <<EMAIL>>"]
repository = "https://github.com/solana-program/token-2022"
homepage = "https://solana-program.com"
license = "Apache-2.0"
edition = "2021"

[workspace.lints.rust.unexpected_cfgs]
level = "warn"
check-cfg = [
    'cfg(target_os, values("solana"))',
    'cfg(feature, values("frozen-abi", "no-entrypoint", "custom-heap", "custom-panic"))',
]

[workspace.metadata.cli]
solana = "2.3.4"

# Specify Rust toolchains for rustfmt, clippy, and build.
# Any unprovided toolchains default to stable.
[workspace.metadata.toolchains]
format = "nightly-2025-02-16"
lint = "nightly-2025-02-16"

[workspace.metadata.spellcheck]
config = "scripts/spellcheck.toml"

[workspace.metadata.release]
pre-release-commit-message = "Publish {{crate_name}} v{{version}}"
tag-message = "Publish {{crate_name}} v{{version}}"
consolidate-commits = false

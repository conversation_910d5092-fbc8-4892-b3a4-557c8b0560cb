[package]
name = "spl-transfer-hook-example-swap"
version = "1.0.0"
description = "Solana Program Library Transfer Hook Example: Swap"
authors = ["Solana Labs Maintainers <<EMAIL>>"]
repository = "https://github.com/solana-labs/solana-program-library"
license = "Apache-2.0"
edition = "2021"
publish = false

[dependencies]
solana-account-info = "2.2.1"
solana-program-entrypoint = "2.2.1"
solana-program-error = "2.2.1"
solana-pubkey = "2.2.1"
spl-token-2022 = { path = "../../../program-2022", features = ["no-entrypoint"] }

[lib]
crate-type = ["cdylib", "lib"]

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

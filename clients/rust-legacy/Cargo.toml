[package]
name = "spl-token-client"
version = "0.17.0"
description = "SPL-Token Rust Client"
documentation = "https://docs.rs/spl-token-client"
readme = "README.md"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[features]
default = ["display"]
dev-context-only-utils = [
    "dep:solana-banks-client",
    "dep:solana-banks-interface",
    "dep:solana-program-test",
]
display = ["dep:solana-cli-output"]

[dependencies]
async-trait = "0.1"
bincode = "1.3.2"
bytemuck = "1.23.1"
futures = "0.3.31"
futures-util = "0.3"
solana-account = "2.2.1"
solana-banks-client = { version = "2.3.4", optional = true }
solana-banks-interface = { version = "2.3.4", optional = true }
solana-compute-budget-interface = "2.2.1"
solana-cli-output = { version = "2.2.0", optional = true }
solana-hash = "2.2.1"
solana-instruction = "2.2.1"
solana-message = "2.2.1"
solana-packet = "2.2.1"
solana-program-error = "2.2.1"
solana-program-pack = "2.2.1"
solana-program-test = { version = "2.3.4", optional = true }
solana-pubkey = "2.2.1"
solana-rpc-client = "2.3.4"
solana-rpc-client-api = "2.3.4"
solana-signature = "2.2.1"
solana-signer = "2.2.1"
solana-system-interface = "1"
solana-transaction = "2.2.1"
spl-associated-token-account-client = { version = "2.0.0" }
spl-elgamal-registry = { version = "0.3.0",features = ["no-entrypoint"], path = "../../confidential/elgamal-registry"}
spl-memo = { version = "6.0", features = ["no-entrypoint"] }
spl-record = { version = "0.3.0", features = ["no-entrypoint"] }
spl-token = { version = "8.0", features = ["no-entrypoint"] }
spl-token-confidential-transfer-proof-extraction = { version = "0.4.1" }
spl-token-confidential-transfer-proof-generation = { version = "0.4.1" }
spl-token-2022 = { version = "9.0.0", features = ["no-entrypoint"], path = "../../program" }
spl-token-group-interface = { version = "0.6.0" }
spl-token-metadata-interface = { version = "0.7.0" }
spl-transfer-hook-interface = { version = "0.10.0" }
tokio = "1"
thiserror = "2.0"

[dev-dependencies]
async-trait = "0.1"
borsh = "1.5.7"
bytemuck = "1.23.1"
futures-util = "0.3"
solana-program = "2.3.0"
solana-program-test = "2.3.4"
solana-sdk = "2.2.1"
spl-associated-token-account = { version = "7.0.0" }
spl-pod = { version = "0.5.1" }
spl-instruction-padding = { version = "0.3.0", features = ["no-entrypoint"] }
spl-tlv-account-resolution = { version = "0.10.0" }
spl-token-client = { path = ".", features = ["dev-context-only-utils"] }
test-case = "3.3"

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBytesDecoder,
  getBytesEncoder,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type ReadonlyUint8Array,
} from '@solana/kit';

/** Authenticated encryption containing an account balance. */
export type DecryptableBalance = ReadonlyUint8Array;

export type DecryptableBalanceArgs = DecryptableBalance;

export function getDecryptableBalanceEncoder(): FixedSizeEncoder<DecryptableBalanceArgs> {
  return fixEncoderSize(getBytesEncoder(), 36);
}

export function getDecryptableBalanceDecoder(): FixedSizeDecoder<DecryptableBalance> {
  return fixDecoderSize(getBytesDecoder(), 36);
}

export function getDecryptableBalanceCodec(): FixedSizeCodec<
  DecryptableBalanceArgs,
  DecryptableBalance
> {
  return combineCodec(
    getDecryptableBalanceEncoder(),
    getDecryptableBalanceDecoder()
  );
}

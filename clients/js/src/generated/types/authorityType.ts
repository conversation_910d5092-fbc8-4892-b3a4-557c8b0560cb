/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getEnumDecoder,
  getEnumEncoder,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
} from '@solana/kit';

export enum AuthorityType {
  MintTokens,
  FreezeAccount,
  AccountOwner,
  CloseAccount,
  TransferFeeConfig,
  WithheldWithdraw,
  CloseMint,
  InterestRate,
  PermanentDelegate,
  ConfidentialTransferMint,
  TransferHookProgramId,
  ConfidentialTransferFeeConfig,
  MetadataPointer,
  GroupPointer,
  GroupMemberPointer,
  ScaledUiAmount,
  Pause,
}

export type AuthorityTypeArgs = AuthorityType;

export function getAuthorityTypeEncoder(): FixedSizeEncoder<AuthorityTypeArgs> {
  return getEnumEncoder(AuthorityType);
}

export function getAuthorityTypeDecoder(): FixedSizeDecoder<AuthorityType> {
  return getEnumDecoder(AuthorityType);
}

export function getAuthorityTypeCodec(): FixedSizeCodec<
  AuthorityTypeArgs,
  AuthorityType
> {
  return combineCodec(getAuthorityTypeEncoder(), getAuthorityTypeDecoder());
}

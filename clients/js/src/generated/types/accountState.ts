/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getEnumDecoder,
  getEnumEncoder,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
} from '@solana/kit';

export enum AccountState {
  Uninitialized,
  Initialized,
  Frozen,
}

export type AccountStateArgs = AccountState;

export function getAccountStateEncoder(): FixedSizeEncoder<AccountStateArgs> {
  return getEnumEncoder(AccountState);
}

export function getAccountStateDecoder(): FixedSizeDecoder<AccountState> {
  return getEnumDecoder(AccountState);
}

export function getAccountStateCodec(): FixedSizeCodec<
  AccountStateArgs,
  AccountState
> {
  return combineCodec(getAccountStateEncoder(), getAccountStateDecoder());
}

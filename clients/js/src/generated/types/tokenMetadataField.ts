/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  addDecoderSizePrefix,
  addEncoderSizePrefix,
  combineCodec,
  getDiscriminatedUnionDecoder,
  getDiscriminatedUnionEncoder,
  getStructDecoder,
  getStructEncoder,
  getTupleDecoder,
  getTupleEncoder,
  getU32Decoder,
  getU32Encoder,
  getUnitDecoder,
  getUnitEncoder,
  getUtf8Decoder,
  getUtf8Encoder,
  type Codec,
  type Decoder,
  type Encoder,
  type GetDiscriminatedUnionVariant,
  type GetDiscriminatedUnionVariantContent,
} from '@solana/kit';

/** Fields in the metadata account, used for updating. */
export type TokenMetadataField =
  | { __kind: 'Name' }
  | { __kind: 'Symbol' }
  | { __kind: 'Uri' }
  | { __kind: 'Key'; fields: readonly [string] };

export type TokenMetadataFieldArgs = TokenMetadataField;

export function getTokenMetadataFieldEncoder(): Encoder<TokenMetadataFieldArgs> {
  return getDiscriminatedUnionEncoder([
    ['Name', getUnitEncoder()],
    ['Symbol', getUnitEncoder()],
    ['Uri', getUnitEncoder()],
    [
      'Key',
      getStructEncoder([
        [
          'fields',
          getTupleEncoder([
            addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder()),
          ]),
        ],
      ]),
    ],
  ]);
}

export function getTokenMetadataFieldDecoder(): Decoder<TokenMetadataField> {
  return getDiscriminatedUnionDecoder([
    ['Name', getUnitDecoder()],
    ['Symbol', getUnitDecoder()],
    ['Uri', getUnitDecoder()],
    [
      'Key',
      getStructDecoder([
        [
          'fields',
          getTupleDecoder([
            addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder()),
          ]),
        ],
      ]),
    ],
  ]);
}

export function getTokenMetadataFieldCodec(): Codec<
  TokenMetadataFieldArgs,
  TokenMetadataField
> {
  return combineCodec(
    getTokenMetadataFieldEncoder(),
    getTokenMetadataFieldDecoder()
  );
}

// Data Enum Helpers.
export function tokenMetadataField(
  kind: 'Name'
): GetDiscriminatedUnionVariant<TokenMetadataFieldArgs, '__kind', 'Name'>;
export function tokenMetadataField(
  kind: 'Symbol'
): GetDiscriminatedUnionVariant<TokenMetadataFieldArgs, '__kind', 'Symbol'>;
export function tokenMetadataField(
  kind: 'Uri'
): GetDiscriminatedUnionVariant<TokenMetadataFieldArgs, '__kind', 'Uri'>;
export function tokenMetadataField(
  kind: 'Key',
  data: GetDiscriminatedUnionVariantContent<
    TokenMetadataFieldArgs,
    '__kind',
    'Key'
  >['fields']
): GetDiscriminatedUnionVariant<TokenMetadataFieldArgs, '__kind', 'Key'>;
export function tokenMetadataField<
  K extends TokenMetadataFieldArgs['__kind'],
  Data,
>(kind: K, data?: Data) {
  return Array.isArray(data)
    ? { __kind: kind, fields: data }
    : { __kind: kind, ...(data ?? {}) };
}

export function isTokenMetadataField<K extends TokenMetadataField['__kind']>(
  kind: K,
  value: TokenMetadataField
): value is TokenMetadataField & { __kind: K } {
  return value.__kind === kind;
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getEnumDecoder,
  getEnumEncoder,
  getU16Decoder,
  getU16Encoder,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
} from '@solana/kit';

/**
 * Extensions that can be applied to mints or accounts.  Mint extensions must
 * only be applied to mint accounts, and account extensions must only be
 * applied to token holding accounts.
 */

export enum ExtensionType {
  Uninitialized,
  TransferFeeConfig,
  TransferFeeAmount,
  MintCloseAuthority,
  ConfidentialTransferMint,
  ConfidentialTransferAccount,
  DefaultAccountState,
  ImmutableOwner,
  MemoTransfer,
  NonTransferable,
  InterestBearingConfig,
  CpiGuard,
  PermanentDelegate,
  NonTransferableAccount,
  TransferHook,
  TransferHookAccount,
  ConfidentialTransferFee,
  ConfidentialTransferFeeAmount,
  ScaledUiAmountConfig,
  PausableConfig,
  PausableAccount,
  MetadataPointer,
  TokenMetadata,
  GroupPointer,
  TokenGroup,
  GroupMemberPointer,
  TokenGroupMember,
}

export type ExtensionTypeArgs = ExtensionType;

export function getExtensionTypeEncoder(): FixedSizeEncoder<ExtensionTypeArgs> {
  return getEnumEncoder(ExtensionType, { size: getU16Encoder() });
}

export function getExtensionTypeDecoder(): FixedSizeDecoder<ExtensionType> {
  return getEnumDecoder(ExtensionType, { size: getU16Decoder() });
}

export function getExtensionTypeCodec(): FixedSizeCodec<
  ExtensionTypeArgs,
  ExtensionType
> {
  return combineCodec(getExtensionTypeEncoder(), getExtensionTypeDecoder());
}

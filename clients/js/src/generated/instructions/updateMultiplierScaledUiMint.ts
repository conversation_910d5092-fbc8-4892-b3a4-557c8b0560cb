/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getF64Decoder,
  getF64Encoder,
  getI64Decoder,
  getI64Encoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR = 43;

export function getUpdateMultiplierScaledUiMintDiscriminatorBytes() {
  return getU8Encoder().encode(UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR);
}

export const UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR = 1;

export function getUpdateMultiplierScaledUiMintScaledUiAmountMintDiscriminatorBytes() {
  return getU8Encoder().encode(
    UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR
  );
}

export type UpdateMultiplierScaledUiMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountAuthority extends string
        ? WritableAccount<TAccountAuthority>
        : TAccountAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type UpdateMultiplierScaledUiMintInstructionData = {
  discriminator: number;
  scaledUiAmountMintDiscriminator: number;
  /** The new multiplier for the scaled UI extension */
  multiplier: number;
  /** The timestamp at which the new multiplier will take effect */
  effectiveTimestamp: bigint;
};

export type UpdateMultiplierScaledUiMintInstructionDataArgs = {
  /** The new multiplier for the scaled UI extension */
  multiplier: number;
  /** The timestamp at which the new multiplier will take effect */
  effectiveTimestamp: number | bigint;
};

export function getUpdateMultiplierScaledUiMintInstructionDataEncoder(): FixedSizeEncoder<UpdateMultiplierScaledUiMintInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['scaledUiAmountMintDiscriminator', getU8Encoder()],
      ['multiplier', getF64Encoder()],
      ['effectiveTimestamp', getI64Encoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR,
      scaledUiAmountMintDiscriminator:
        UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR,
    })
  );
}

export function getUpdateMultiplierScaledUiMintInstructionDataDecoder(): FixedSizeDecoder<UpdateMultiplierScaledUiMintInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['scaledUiAmountMintDiscriminator', getU8Decoder()],
    ['multiplier', getF64Decoder()],
    ['effectiveTimestamp', getI64Decoder()],
  ]);
}

export function getUpdateMultiplierScaledUiMintInstructionDataCodec(): FixedSizeCodec<
  UpdateMultiplierScaledUiMintInstructionDataArgs,
  UpdateMultiplierScaledUiMintInstructionData
> {
  return combineCodec(
    getUpdateMultiplierScaledUiMintInstructionDataEncoder(),
    getUpdateMultiplierScaledUiMintInstructionDataDecoder()
  );
}

export type UpdateMultiplierScaledUiMintInput<
  TAccountMint extends string = string,
  TAccountAuthority extends string = string,
> = {
  /** The mint. */
  mint: Address<TAccountMint>;
  /** The multiplier authority. */
  authority: Address<TAccountAuthority> | TransactionSigner<TAccountAuthority>;
  multiplier: UpdateMultiplierScaledUiMintInstructionDataArgs['multiplier'];
  effectiveTimestamp: UpdateMultiplierScaledUiMintInstructionDataArgs['effectiveTimestamp'];
  multiSigners?: Array<TransactionSigner>;
};

export function getUpdateMultiplierScaledUiMintInstruction<
  TAccountMint extends string,
  TAccountAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: UpdateMultiplierScaledUiMintInput<TAccountMint, TAccountAuthority>,
  config?: { programAddress?: TProgramAddress }
): UpdateMultiplierScaledUiMintInstruction<
  TProgramAddress,
  TAccountMint,
  (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
    ? WritableSignerAccount<TAccountAuthority> &
        AccountSignerMeta<TAccountAuthority>
    : TAccountAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getUpdateMultiplierScaledUiMintInstructionDataEncoder().encode(
      args as UpdateMultiplierScaledUiMintInstructionDataArgs
    ),
  } as UpdateMultiplierScaledUiMintInstruction<
    TProgramAddress,
    TAccountMint,
    (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
      ? WritableSignerAccount<TAccountAuthority> &
          AccountSignerMeta<TAccountAuthority>
      : TAccountAuthority
  >;

  return instruction;
}

export type ParsedUpdateMultiplierScaledUiMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint. */
    mint: TAccountMetas[0];
    /** The multiplier authority. */
    authority: TAccountMetas[1];
  };
  data: UpdateMultiplierScaledUiMintInstructionData;
};

export function parseUpdateMultiplierScaledUiMintInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedUpdateMultiplierScaledUiMintInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount(),
    },
    data: getUpdateMultiplierScaledUiMintInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

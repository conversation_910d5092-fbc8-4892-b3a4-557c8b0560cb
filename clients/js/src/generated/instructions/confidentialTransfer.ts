/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getI8Decoder,
  getI8Encoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';
import {
  getDecryptableBalanceDecoder,
  getDecryptableBalanceEncoder,
  type DecryptableBalance,
  type DecryptableBalanceArgs,
} from '../types';

export const CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 27;

export function getConfidentialTransferDiscriminatorBytes() {
  return getU8Encoder().encode(CONFIDENTIAL_TRANSFER_DISCRIMINATOR);
}

export const CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 7;

export function getConfidentialTransferConfidentialTransferDiscriminatorBytes() {
  return getU8Encoder().encode(
    CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}

export type ConfidentialTransferInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountSourceToken extends string | AccountMeta<string> = string,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountDestinationToken extends string | AccountMeta<string> = string,
  TAccountInstructionsSysvar extends string | AccountMeta<string> = string,
  TAccountEqualityRecord extends string | AccountMeta<string> = string,
  TAccountCiphertextValidityRecord extends
    | string
    | AccountMeta<string> = string,
  TAccountRangeRecord extends string | AccountMeta<string> = string,
  TAccountAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountSourceToken extends string
        ? WritableAccount<TAccountSourceToken>
        : TAccountSourceToken,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountDestinationToken extends string
        ? WritableAccount<TAccountDestinationToken>
        : TAccountDestinationToken,
      TAccountInstructionsSysvar extends string
        ? ReadonlyAccount<TAccountInstructionsSysvar>
        : TAccountInstructionsSysvar,
      TAccountEqualityRecord extends string
        ? ReadonlyAccount<TAccountEqualityRecord>
        : TAccountEqualityRecord,
      TAccountCiphertextValidityRecord extends string
        ? ReadonlyAccount<TAccountCiphertextValidityRecord>
        : TAccountCiphertextValidityRecord,
      TAccountRangeRecord extends string
        ? ReadonlyAccount<TAccountRangeRecord>
        : TAccountRangeRecord,
      TAccountAuthority extends string
        ? ReadonlyAccount<TAccountAuthority>
        : TAccountAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type ConfidentialTransferInstructionData = {
  discriminator: number;
  confidentialTransferDiscriminator: number;
  /** The new source decryptable balance if the transfer succeeds. */
  newSourceDecryptableAvailableBalance: DecryptableBalance;
  /**
   * Relative location of the
   * `ProofInstruction::VerifyCiphertextCommitmentEquality` instruction
   * to the `Transfer` instruction in the transaction. If the offset is
   * `0`, then use a context state account for the proof.
   */
  equalityProofInstructionOffset: number;
  /**
   * Relative location of the
   * `ProofInstruction::VerifyBatchedGroupedCiphertext3HandlesValidity`
   * instruction to the `Transfer` instruction in the transaction. If the
   * offset is `0`, then use a context state account for the proof.
   */
  ciphertextValidityProofInstructionOffset: number;
  /**
   * Relative location of the `ProofInstruction::BatchedRangeProofU128Data`
   * instruction to the `Transfer` instruction in the transaction. If the
   * offset is `0`, then use a context state account for the proof.
   */
  rangeProofInstructionOffset: number;
};

export type ConfidentialTransferInstructionDataArgs = {
  /** The new source decryptable balance if the transfer succeeds. */
  newSourceDecryptableAvailableBalance: DecryptableBalanceArgs;
  /**
   * Relative location of the
   * `ProofInstruction::VerifyCiphertextCommitmentEquality` instruction
   * to the `Transfer` instruction in the transaction. If the offset is
   * `0`, then use a context state account for the proof.
   */
  equalityProofInstructionOffset: number;
  /**
   * Relative location of the
   * `ProofInstruction::VerifyBatchedGroupedCiphertext3HandlesValidity`
   * instruction to the `Transfer` instruction in the transaction. If the
   * offset is `0`, then use a context state account for the proof.
   */
  ciphertextValidityProofInstructionOffset: number;
  /**
   * Relative location of the `ProofInstruction::BatchedRangeProofU128Data`
   * instruction to the `Transfer` instruction in the transaction. If the
   * offset is `0`, then use a context state account for the proof.
   */
  rangeProofInstructionOffset: number;
};

export function getConfidentialTransferInstructionDataEncoder(): FixedSizeEncoder<ConfidentialTransferInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['confidentialTransferDiscriminator', getU8Encoder()],
      ['newSourceDecryptableAvailableBalance', getDecryptableBalanceEncoder()],
      ['equalityProofInstructionOffset', getI8Encoder()],
      ['ciphertextValidityProofInstructionOffset', getI8Encoder()],
      ['rangeProofInstructionOffset', getI8Encoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: CONFIDENTIAL_TRANSFER_DISCRIMINATOR,
      confidentialTransferDiscriminator:
        CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR,
    })
  );
}

export function getConfidentialTransferInstructionDataDecoder(): FixedSizeDecoder<ConfidentialTransferInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['confidentialTransferDiscriminator', getU8Decoder()],
    ['newSourceDecryptableAvailableBalance', getDecryptableBalanceDecoder()],
    ['equalityProofInstructionOffset', getI8Decoder()],
    ['ciphertextValidityProofInstructionOffset', getI8Decoder()],
    ['rangeProofInstructionOffset', getI8Decoder()],
  ]);
}

export function getConfidentialTransferInstructionDataCodec(): FixedSizeCodec<
  ConfidentialTransferInstructionDataArgs,
  ConfidentialTransferInstructionData
> {
  return combineCodec(
    getConfidentialTransferInstructionDataEncoder(),
    getConfidentialTransferInstructionDataDecoder()
  );
}

export type ConfidentialTransferInput<
  TAccountSourceToken extends string = string,
  TAccountMint extends string = string,
  TAccountDestinationToken extends string = string,
  TAccountInstructionsSysvar extends string = string,
  TAccountEqualityRecord extends string = string,
  TAccountCiphertextValidityRecord extends string = string,
  TAccountRangeRecord extends string = string,
  TAccountAuthority extends string = string,
> = {
  /** The source SPL Token account. */
  sourceToken: Address<TAccountSourceToken>;
  /** The corresponding SPL Token mint. */
  mint: Address<TAccountMint>;
  /** The destination SPL Token account. */
  destinationToken: Address<TAccountDestinationToken>;
  /**
   * (Optional) Instructions sysvar if at least one of the
   * `zk_elgamal_proof` instructions are included in the same
   * transaction.
   */
  instructionsSysvar?: Address<TAccountInstructionsSysvar>;
  /** (Optional) Equality proof record account or context state account. */
  equalityRecord?: Address<TAccountEqualityRecord>;
  /** (Optional) Ciphertext validity proof record account or context state account. */
  ciphertextValidityRecord?: Address<TAccountCiphertextValidityRecord>;
  /** (Optional) Range proof record account or context state account. */
  rangeRecord?: Address<TAccountRangeRecord>;
  /** The source account's owner/delegate or its multisignature account. */
  authority: Address<TAccountAuthority> | TransactionSigner<TAccountAuthority>;
  newSourceDecryptableAvailableBalance: ConfidentialTransferInstructionDataArgs['newSourceDecryptableAvailableBalance'];
  equalityProofInstructionOffset: ConfidentialTransferInstructionDataArgs['equalityProofInstructionOffset'];
  ciphertextValidityProofInstructionOffset: ConfidentialTransferInstructionDataArgs['ciphertextValidityProofInstructionOffset'];
  rangeProofInstructionOffset: ConfidentialTransferInstructionDataArgs['rangeProofInstructionOffset'];
  multiSigners?: Array<TransactionSigner>;
};

export function getConfidentialTransferInstruction<
  TAccountSourceToken extends string,
  TAccountMint extends string,
  TAccountDestinationToken extends string,
  TAccountInstructionsSysvar extends string,
  TAccountEqualityRecord extends string,
  TAccountCiphertextValidityRecord extends string,
  TAccountRangeRecord extends string,
  TAccountAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: ConfidentialTransferInput<
    TAccountSourceToken,
    TAccountMint,
    TAccountDestinationToken,
    TAccountInstructionsSysvar,
    TAccountEqualityRecord,
    TAccountCiphertextValidityRecord,
    TAccountRangeRecord,
    TAccountAuthority
  >,
  config?: { programAddress?: TProgramAddress }
): ConfidentialTransferInstruction<
  TProgramAddress,
  TAccountSourceToken,
  TAccountMint,
  TAccountDestinationToken,
  TAccountInstructionsSysvar,
  TAccountEqualityRecord,
  TAccountCiphertextValidityRecord,
  TAccountRangeRecord,
  (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
    ? ReadonlySignerAccount<TAccountAuthority> &
        AccountSignerMeta<TAccountAuthority>
    : TAccountAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    sourceToken: { value: input.sourceToken ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    destinationToken: {
      value: input.destinationToken ?? null,
      isWritable: true,
    },
    instructionsSysvar: {
      value: input.instructionsSysvar ?? null,
      isWritable: false,
    },
    equalityRecord: { value: input.equalityRecord ?? null, isWritable: false },
    ciphertextValidityRecord: {
      value: input.ciphertextValidityRecord ?? null,
      isWritable: false,
    },
    rangeRecord: { value: input.rangeRecord ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sourceToken),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destinationToken),
      getAccountMeta(accounts.instructionsSysvar),
      getAccountMeta(accounts.equalityRecord),
      getAccountMeta(accounts.ciphertextValidityRecord),
      getAccountMeta(accounts.rangeRecord),
      getAccountMeta(accounts.authority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getConfidentialTransferInstructionDataEncoder().encode(
      args as ConfidentialTransferInstructionDataArgs
    ),
  } as ConfidentialTransferInstruction<
    TProgramAddress,
    TAccountSourceToken,
    TAccountMint,
    TAccountDestinationToken,
    TAccountInstructionsSysvar,
    TAccountEqualityRecord,
    TAccountCiphertextValidityRecord,
    TAccountRangeRecord,
    (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
      ? ReadonlySignerAccount<TAccountAuthority> &
          AccountSignerMeta<TAccountAuthority>
      : TAccountAuthority
  >;

  return instruction;
}

export type ParsedConfidentialTransferInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The source SPL Token account. */
    sourceToken: TAccountMetas[0];
    /** The corresponding SPL Token mint. */
    mint: TAccountMetas[1];
    /** The destination SPL Token account. */
    destinationToken: TAccountMetas[2];
    /**
     * (Optional) Instructions sysvar if at least one of the
     * `zk_elgamal_proof` instructions are included in the same
     * transaction.
     */

    instructionsSysvar?: TAccountMetas[3] | undefined;
    /** (Optional) Equality proof record account or context state account. */
    equalityRecord?: TAccountMetas[4] | undefined;
    /** (Optional) Ciphertext validity proof record account or context state account. */
    ciphertextValidityRecord?: TAccountMetas[5] | undefined;
    /** (Optional) Range proof record account or context state account. */
    rangeRecord?: TAccountMetas[6] | undefined;
    /** The source account's owner/delegate or its multisignature account. */
    authority: TAccountMetas[7];
  };
  data: ConfidentialTransferInstructionData;
};

export function parseConfidentialTransferInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedConfidentialTransferInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 8) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS
      ? undefined
      : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sourceToken: getNextAccount(),
      mint: getNextAccount(),
      destinationToken: getNextAccount(),
      instructionsSysvar: getNextOptionalAccount(),
      equalityRecord: getNextOptionalAccount(),
      ciphertextValidityRecord: getNextOptionalAccount(),
      rangeRecord: getNextOptionalAccount(),
      authority: getNextAccount(),
    },
    data: getConfidentialTransferInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

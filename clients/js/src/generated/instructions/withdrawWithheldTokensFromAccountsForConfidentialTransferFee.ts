/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getI8Decoder,
  getI8Encoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';
import {
  getDecryptableBalanceDecoder,
  getDecryptableBalanceEncoder,
  type DecryptableBalance,
  type DecryptableBalanceArgs,
} from '../types';

export const WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 37;

export function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeDiscriminatorBytes() {
  return getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}

export const WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 2;

export function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes() {
  return getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}

export type WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountDestination extends string | AccountMeta<string> = string,
  TAccountInstructionsSysvarOrContextState extends
    | string
    | AccountMeta<string> = string,
  TAccountRecord extends string | AccountMeta<string> = string,
  TAccountAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountDestination extends string
        ? WritableAccount<TAccountDestination>
        : TAccountDestination,
      TAccountInstructionsSysvarOrContextState extends string
        ? ReadonlyAccount<TAccountInstructionsSysvarOrContextState>
        : TAccountInstructionsSysvarOrContextState,
      TAccountRecord extends string
        ? ReadonlyAccount<TAccountRecord>
        : TAccountRecord,
      TAccountAuthority extends string
        ? ReadonlyAccount<TAccountAuthority>
        : TAccountAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionData =
  {
    discriminator: number;
    confidentialTransferFeeDiscriminator: number;
    /** Number of token accounts harvested */
    numTokenAccounts: number;
    /** Proof instruction offset */
    proofInstructionOffset: number;
    /** The new decryptable balance in the destination token account */
    newDecryptableAvailableBalance: DecryptableBalance;
  };

export type WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataArgs =
  {
    /** Number of token accounts harvested */
    numTokenAccounts: number;
    /** Proof instruction offset */
    proofInstructionOffset: number;
    /** The new decryptable balance in the destination token account */
    newDecryptableAvailableBalance: DecryptableBalanceArgs;
  };

export function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder(): FixedSizeEncoder<WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['confidentialTransferFeeDiscriminator', getU8Encoder()],
      ['numTokenAccounts', getU8Encoder()],
      ['proofInstructionOffset', getI8Encoder()],
      ['newDecryptableAvailableBalance', getDecryptableBalanceEncoder()],
    ]),
    (value) => ({
      ...value,
      discriminator:
        WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR,
      confidentialTransferFeeDiscriminator:
        WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR,
    })
  );
}

export function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder(): FixedSizeDecoder<WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['confidentialTransferFeeDiscriminator', getU8Decoder()],
    ['numTokenAccounts', getU8Decoder()],
    ['proofInstructionOffset', getI8Decoder()],
    ['newDecryptableAvailableBalance', getDecryptableBalanceDecoder()],
  ]);
}

export function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataCodec(): FixedSizeCodec<
  WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataArgs,
  WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionData
> {
  return combineCodec(
    getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder(),
    getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder()
  );
}

export type WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInput<
  TAccountMint extends string = string,
  TAccountDestination extends string = string,
  TAccountInstructionsSysvarOrContextState extends string = string,
  TAccountRecord extends string = string,
  TAccountAuthority extends string = string,
> = {
  /** The token mint. */
  mint: Address<TAccountMint>;
  /** The fee receiver account. */
  destination: Address<TAccountDestination>;
  /** Instructions sysvar or context state account */
  instructionsSysvarOrContextState: Address<TAccountInstructionsSysvarOrContextState>;
  /** Optional record account */
  record?: Address<TAccountRecord>;
  /** The mint's withdraw_withheld_authority */
  authority: Address<TAccountAuthority> | TransactionSigner<TAccountAuthority>;
  numTokenAccounts: WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataArgs['numTokenAccounts'];
  proofInstructionOffset: WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataArgs['proofInstructionOffset'];
  newDecryptableAvailableBalance: WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataArgs['newDecryptableAvailableBalance'];
  multiSigners?: Array<TransactionSigner>;
};

export function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction<
  TAccountMint extends string,
  TAccountDestination extends string,
  TAccountInstructionsSysvarOrContextState extends string,
  TAccountRecord extends string,
  TAccountAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInput<
    TAccountMint,
    TAccountDestination,
    TAccountInstructionsSysvarOrContextState,
    TAccountRecord,
    TAccountAuthority
  >,
  config?: { programAddress?: TProgramAddress }
): WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction<
  TProgramAddress,
  TAccountMint,
  TAccountDestination,
  TAccountInstructionsSysvarOrContextState,
  TAccountRecord,
  (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
    ? ReadonlySignerAccount<TAccountAuthority> &
        AccountSignerMeta<TAccountAuthority>
    : TAccountAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: false },
    destination: { value: input.destination ?? null, isWritable: true },
    instructionsSysvarOrContextState: {
      value: input.instructionsSysvarOrContextState ?? null,
      isWritable: false,
    },
    record: { value: input.record ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.instructionsSysvarOrContextState),
      getAccountMeta(accounts.record),
      getAccountMeta(accounts.authority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder().encode(
      args as WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataArgs
    ),
  } as WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction<
    TProgramAddress,
    TAccountMint,
    TAccountDestination,
    TAccountInstructionsSysvarOrContextState,
    TAccountRecord,
    (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
      ? ReadonlySignerAccount<TAccountAuthority> &
          AccountSignerMeta<TAccountAuthority>
      : TAccountAuthority
  >;

  return instruction;
}

export type ParsedWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The token mint. */
    mint: TAccountMetas[0];
    /** The fee receiver account. */
    destination: TAccountMetas[1];
    /** Instructions sysvar or context state account */
    instructionsSysvarOrContextState: TAccountMetas[2];
    /** Optional record account */
    record?: TAccountMetas[3] | undefined;
    /** The mint's withdraw_withheld_authority */
    authority: TAccountMetas[4];
  };
  data: WithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionData;
};

export function parseWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction<
  TProgram,
  TAccountMetas
> {
  if (instruction.accounts.length < 5) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS
      ? undefined
      : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      destination: getNextAccount(),
      instructionsSysvarOrContextState: getNextAccount(),
      record: getNextOptionalAccount(),
      authority: getNextAccount(),
    },
    data: getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getI16Decoder,
  getI16Encoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR = 33;

export function getUpdateRateInterestBearingMintDiscriminatorBytes() {
  return getU8Encoder().encode(UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR);
}

export const UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR = 1;

export function getUpdateRateInterestBearingMintInterestBearingMintDiscriminatorBytes() {
  return getU8Encoder().encode(
    UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR
  );
}

export type UpdateRateInterestBearingMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountRateAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountRateAuthority extends string
        ? WritableAccount<TAccountRateAuthority>
        : TAccountRateAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type UpdateRateInterestBearingMintInstructionData = {
  discriminator: number;
  interestBearingMintDiscriminator: number;
  /** The interest rate to update. */
  rate: number;
};

export type UpdateRateInterestBearingMintInstructionDataArgs = {
  /** The interest rate to update. */
  rate: number;
};

export function getUpdateRateInterestBearingMintInstructionDataEncoder(): FixedSizeEncoder<UpdateRateInterestBearingMintInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['interestBearingMintDiscriminator', getU8Encoder()],
      ['rate', getI16Encoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR,
      interestBearingMintDiscriminator:
        UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR,
    })
  );
}

export function getUpdateRateInterestBearingMintInstructionDataDecoder(): FixedSizeDecoder<UpdateRateInterestBearingMintInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['interestBearingMintDiscriminator', getU8Decoder()],
    ['rate', getI16Decoder()],
  ]);
}

export function getUpdateRateInterestBearingMintInstructionDataCodec(): FixedSizeCodec<
  UpdateRateInterestBearingMintInstructionDataArgs,
  UpdateRateInterestBearingMintInstructionData
> {
  return combineCodec(
    getUpdateRateInterestBearingMintInstructionDataEncoder(),
    getUpdateRateInterestBearingMintInstructionDataDecoder()
  );
}

export type UpdateRateInterestBearingMintInput<
  TAccountMint extends string = string,
  TAccountRateAuthority extends string = string,
> = {
  /** The mint. */
  mint: Address<TAccountMint>;
  /** The mint rate authority. */
  rateAuthority:
    | Address<TAccountRateAuthority>
    | TransactionSigner<TAccountRateAuthority>;
  rate: UpdateRateInterestBearingMintInstructionDataArgs['rate'];
  multiSigners?: Array<TransactionSigner>;
};

export function getUpdateRateInterestBearingMintInstruction<
  TAccountMint extends string,
  TAccountRateAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: UpdateRateInterestBearingMintInput<
    TAccountMint,
    TAccountRateAuthority
  >,
  config?: { programAddress?: TProgramAddress }
): UpdateRateInterestBearingMintInstruction<
  TProgramAddress,
  TAccountMint,
  (typeof input)['rateAuthority'] extends TransactionSigner<TAccountRateAuthority>
    ? WritableSignerAccount<TAccountRateAuthority> &
        AccountSignerMeta<TAccountRateAuthority>
    : TAccountRateAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    rateAuthority: { value: input.rateAuthority ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.rateAuthority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getUpdateRateInterestBearingMintInstructionDataEncoder().encode(
      args as UpdateRateInterestBearingMintInstructionDataArgs
    ),
  } as UpdateRateInterestBearingMintInstruction<
    TProgramAddress,
    TAccountMint,
    (typeof input)['rateAuthority'] extends TransactionSigner<TAccountRateAuthority>
      ? WritableSignerAccount<TAccountRateAuthority> &
          AccountSignerMeta<TAccountRateAuthority>
      : TAccountRateAuthority
  >;

  return instruction;
}

export type ParsedUpdateRateInterestBearingMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint. */
    mint: TAccountMetas[0];
    /** The mint rate authority. */
    rateAuthority: TAccountMetas[1];
  };
  data: UpdateRateInterestBearingMintInstructionData;
};

export function parseUpdateRateInterestBearingMintInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedUpdateRateInterestBearingMintInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      rateAuthority: getNextAccount(),
    },
    data: getUpdateRateInterestBearingMintInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const RESUME_DISCRIMINATOR = 44;

export function getResumeDiscriminatorBytes() {
  return getU8Encoder().encode(RESUME_DISCRIMINATOR);
}

export const RESUME_PAUSABLE_DISCRIMINATOR = 2;

export function getResumePausableDiscriminatorBytes() {
  return getU8Encoder().encode(RESUME_PAUSABLE_DISCRIMINATOR);
}

export type ResumeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountAuthority extends string
        ? ReadonlyAccount<TAccountAuthority>
        : TAccountAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type ResumeInstructionData = {
  discriminator: number;
  pausableDiscriminator: number;
};

export type ResumeInstructionDataArgs = {};

export function getResumeInstructionDataEncoder(): FixedSizeEncoder<ResumeInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['pausableDiscriminator', getU8Encoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: RESUME_DISCRIMINATOR,
      pausableDiscriminator: RESUME_PAUSABLE_DISCRIMINATOR,
    })
  );
}

export function getResumeInstructionDataDecoder(): FixedSizeDecoder<ResumeInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['pausableDiscriminator', getU8Decoder()],
  ]);
}

export function getResumeInstructionDataCodec(): FixedSizeCodec<
  ResumeInstructionDataArgs,
  ResumeInstructionData
> {
  return combineCodec(
    getResumeInstructionDataEncoder(),
    getResumeInstructionDataDecoder()
  );
}

export type ResumeInput<
  TAccountMint extends string = string,
  TAccountAuthority extends string = string,
> = {
  /** The mint. */
  mint: Address<TAccountMint>;
  /** The pausable authority that can resume the mint. */
  authority: Address<TAccountAuthority> | TransactionSigner<TAccountAuthority>;
};

export function getResumeInstruction<
  TAccountMint extends string,
  TAccountAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: ResumeInput<TAccountMint, TAccountAuthority>,
  config?: { programAddress?: TProgramAddress }
): ResumeInstruction<
  TProgramAddress,
  TAccountMint,
  (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
    ? ReadonlySignerAccount<TAccountAuthority> &
        AccountSignerMeta<TAccountAuthority>
    : TAccountAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
    ],
    programAddress,
    data: getResumeInstructionDataEncoder().encode({}),
  } as ResumeInstruction<
    TProgramAddress,
    TAccountMint,
    (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
      ? ReadonlySignerAccount<TAccountAuthority> &
          AccountSignerMeta<TAccountAuthority>
      : TAccountAuthority
  >;

  return instruction;
}

export type ParsedResumeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint. */
    mint: TAccountMetas[0];
    /** The pausable authority that can resume the mint. */
    authority: TAccountMetas[1];
  };
  data: ResumeInstructionData;
};

export function parseResumeInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedResumeInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount(),
    },
    data: getResumeInstructionDataDecoder().decode(instruction.data),
  };
}

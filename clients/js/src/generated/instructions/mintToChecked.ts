/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const MINT_TO_CHECKED_DISCRIMINATOR = 14;

export function getMintToCheckedDiscriminatorBytes() {
  return getU8Encoder().encode(MINT_TO_CHECKED_DISCRIMINATOR);
}

export type MintToCheckedInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountToken extends string | AccountMeta<string> = string,
  TAccountMintAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountToken extends string
        ? WritableAccount<TAccountToken>
        : TAccountToken,
      TAccountMintAuthority extends string
        ? ReadonlyAccount<TAccountMintAuthority>
        : TAccountMintAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type MintToCheckedInstructionData = {
  discriminator: number;
  /** The amount of new tokens to mint. */
  amount: bigint;
  /** Expected number of base 10 digits to the right of the decimal place. */
  decimals: number;
};

export type MintToCheckedInstructionDataArgs = {
  /** The amount of new tokens to mint. */
  amount: number | bigint;
  /** Expected number of base 10 digits to the right of the decimal place. */
  decimals: number;
};

export function getMintToCheckedInstructionDataEncoder(): FixedSizeEncoder<MintToCheckedInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['amount', getU64Encoder()],
      ['decimals', getU8Encoder()],
    ]),
    (value) => ({ ...value, discriminator: MINT_TO_CHECKED_DISCRIMINATOR })
  );
}

export function getMintToCheckedInstructionDataDecoder(): FixedSizeDecoder<MintToCheckedInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['amount', getU64Decoder()],
    ['decimals', getU8Decoder()],
  ]);
}

export function getMintToCheckedInstructionDataCodec(): FixedSizeCodec<
  MintToCheckedInstructionDataArgs,
  MintToCheckedInstructionData
> {
  return combineCodec(
    getMintToCheckedInstructionDataEncoder(),
    getMintToCheckedInstructionDataDecoder()
  );
}

export type MintToCheckedInput<
  TAccountMint extends string = string,
  TAccountToken extends string = string,
  TAccountMintAuthority extends string = string,
> = {
  /** The mint. */
  mint: Address<TAccountMint>;
  /** The account to mint tokens to. */
  token: Address<TAccountToken>;
  /** The mint's minting authority or its multisignature account. */
  mintAuthority:
    | Address<TAccountMintAuthority>
    | TransactionSigner<TAccountMintAuthority>;
  amount: MintToCheckedInstructionDataArgs['amount'];
  decimals: MintToCheckedInstructionDataArgs['decimals'];
  multiSigners?: Array<TransactionSigner>;
};

export function getMintToCheckedInstruction<
  TAccountMint extends string,
  TAccountToken extends string,
  TAccountMintAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: MintToCheckedInput<TAccountMint, TAccountToken, TAccountMintAuthority>,
  config?: { programAddress?: TProgramAddress }
): MintToCheckedInstruction<
  TProgramAddress,
  TAccountMint,
  TAccountToken,
  (typeof input)['mintAuthority'] extends TransactionSigner<TAccountMintAuthority>
    ? ReadonlySignerAccount<TAccountMintAuthority> &
        AccountSignerMeta<TAccountMintAuthority>
    : TAccountMintAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    token: { value: input.token ?? null, isWritable: true },
    mintAuthority: { value: input.mintAuthority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mintAuthority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getMintToCheckedInstructionDataEncoder().encode(
      args as MintToCheckedInstructionDataArgs
    ),
  } as MintToCheckedInstruction<
    TProgramAddress,
    TAccountMint,
    TAccountToken,
    (typeof input)['mintAuthority'] extends TransactionSigner<TAccountMintAuthority>
      ? ReadonlySignerAccount<TAccountMintAuthority> &
          AccountSignerMeta<TAccountMintAuthority>
      : TAccountMintAuthority
  >;

  return instruction;
}

export type ParsedMintToCheckedInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint. */
    mint: TAccountMetas[0];
    /** The account to mint tokens to. */
    token: TAccountMetas[1];
    /** The mint's minting authority or its multisignature account. */
    mintAuthority: TAccountMetas[2];
  };
  data: MintToCheckedInstructionData;
};

export function parseMintToCheckedInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedMintToCheckedInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 3) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      token: getNextAccount(),
      mintAuthority: getNextAccount(),
    },
    data: getMintToCheckedInstructionDataDecoder().decode(instruction.data),
  };
}

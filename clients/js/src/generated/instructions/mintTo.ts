/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const MINT_TO_DISCRIMINATOR = 7;

export function getMintToDiscriminatorBytes() {
  return getU8Encoder().encode(MINT_TO_DISCRIMINATOR);
}

export type MintToInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountToken extends string | AccountMeta<string> = string,
  TAccountMintAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountToken extends string
        ? WritableAccount<TAccountToken>
        : TAccountToken,
      TAccountMintAuthority extends string
        ? ReadonlyAccount<TAccountMintAuthority>
        : TAccountMintAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type MintToInstructionData = {
  discriminator: number;
  /** The amount of new tokens to mint. */
  amount: bigint;
};

export type MintToInstructionDataArgs = {
  /** The amount of new tokens to mint. */
  amount: number | bigint;
};

export function getMintToInstructionDataEncoder(): FixedSizeEncoder<MintToInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['amount', getU64Encoder()],
    ]),
    (value) => ({ ...value, discriminator: MINT_TO_DISCRIMINATOR })
  );
}

export function getMintToInstructionDataDecoder(): FixedSizeDecoder<MintToInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['amount', getU64Decoder()],
  ]);
}

export function getMintToInstructionDataCodec(): FixedSizeCodec<
  MintToInstructionDataArgs,
  MintToInstructionData
> {
  return combineCodec(
    getMintToInstructionDataEncoder(),
    getMintToInstructionDataDecoder()
  );
}

export type MintToInput<
  TAccountMint extends string = string,
  TAccountToken extends string = string,
  TAccountMintAuthority extends string = string,
> = {
  /** The mint account. */
  mint: Address<TAccountMint>;
  /** The account to mint tokens to. */
  token: Address<TAccountToken>;
  /** The mint's minting authority or its multisignature account. */
  mintAuthority:
    | Address<TAccountMintAuthority>
    | TransactionSigner<TAccountMintAuthority>;
  amount: MintToInstructionDataArgs['amount'];
  multiSigners?: Array<TransactionSigner>;
};

export function getMintToInstruction<
  TAccountMint extends string,
  TAccountToken extends string,
  TAccountMintAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: MintToInput<TAccountMint, TAccountToken, TAccountMintAuthority>,
  config?: { programAddress?: TProgramAddress }
): MintToInstruction<
  TProgramAddress,
  TAccountMint,
  TAccountToken,
  (typeof input)['mintAuthority'] extends TransactionSigner<TAccountMintAuthority>
    ? ReadonlySignerAccount<TAccountMintAuthority> &
        AccountSignerMeta<TAccountMintAuthority>
    : TAccountMintAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    token: { value: input.token ?? null, isWritable: true },
    mintAuthority: { value: input.mintAuthority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mintAuthority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getMintToInstructionDataEncoder().encode(
      args as MintToInstructionDataArgs
    ),
  } as MintToInstruction<
    TProgramAddress,
    TAccountMint,
    TAccountToken,
    (typeof input)['mintAuthority'] extends TransactionSigner<TAccountMintAuthority>
      ? ReadonlySignerAccount<TAccountMintAuthority> &
          AccountSignerMeta<TAccountMintAuthority>
      : TAccountMintAuthority
  >;

  return instruction;
}

export type ParsedMintToInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint account. */
    mint: TAccountMetas[0];
    /** The account to mint tokens to. */
    token: TAccountMetas[1];
    /** The mint's minting authority or its multisignature account. */
    mintAuthority: TAccountMetas[2];
  };
  data: MintToInstructionData;
};

export function parseMintToInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedMintToInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 3) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      token: getNextAccount(),
      mintAuthority: getNextAccount(),
    },
    data: getMintToInstructionDataDecoder().decode(instruction.data),
  };
}

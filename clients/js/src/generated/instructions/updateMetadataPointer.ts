/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const UPDATE_METADATA_POINTER_DISCRIMINATOR = 39;

export function getUpdateMetadataPointerDiscriminatorBytes() {
  return getU8Encoder().encode(UPDATE_METADATA_POINTER_DISCRIMINATOR);
}

export const UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR = 1;

export function getUpdateMetadataPointerMetadataPointerDiscriminatorBytes() {
  return getU8Encoder().encode(
    UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR
  );
}

export type UpdateMetadataPointerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountMetadataPointerAuthority extends
    | string
    | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountMetadataPointerAuthority extends string
        ? ReadonlyAccount<TAccountMetadataPointerAuthority>
        : TAccountMetadataPointerAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type UpdateMetadataPointerInstructionData = {
  discriminator: number;
  metadataPointerDiscriminator: number;
  /** The new account address that holds the metadata. */
  metadataAddress: Option<Address>;
};

export type UpdateMetadataPointerInstructionDataArgs = {
  /** The new account address that holds the metadata. */
  metadataAddress: OptionOrNullable<Address>;
};

export function getUpdateMetadataPointerInstructionDataEncoder(): FixedSizeEncoder<UpdateMetadataPointerInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['metadataPointerDiscriminator', getU8Encoder()],
      [
        'metadataAddress',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_METADATA_POINTER_DISCRIMINATOR,
      metadataPointerDiscriminator:
        UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR,
    })
  );
}

export function getUpdateMetadataPointerInstructionDataDecoder(): FixedSizeDecoder<UpdateMetadataPointerInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['metadataPointerDiscriminator', getU8Decoder()],
    [
      'metadataAddress',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
  ]);
}

export function getUpdateMetadataPointerInstructionDataCodec(): FixedSizeCodec<
  UpdateMetadataPointerInstructionDataArgs,
  UpdateMetadataPointerInstructionData
> {
  return combineCodec(
    getUpdateMetadataPointerInstructionDataEncoder(),
    getUpdateMetadataPointerInstructionDataDecoder()
  );
}

export type UpdateMetadataPointerInput<
  TAccountMint extends string = string,
  TAccountMetadataPointerAuthority extends string = string,
> = {
  /** The mint to initialize. */
  mint: Address<TAccountMint>;
  /** The metadata pointer authority or its multisignature account. */
  metadataPointerAuthority:
    | Address<TAccountMetadataPointerAuthority>
    | TransactionSigner<TAccountMetadataPointerAuthority>;
  metadataAddress: UpdateMetadataPointerInstructionDataArgs['metadataAddress'];
  multiSigners?: Array<TransactionSigner>;
};

export function getUpdateMetadataPointerInstruction<
  TAccountMint extends string,
  TAccountMetadataPointerAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: UpdateMetadataPointerInput<
    TAccountMint,
    TAccountMetadataPointerAuthority
  >,
  config?: { programAddress?: TProgramAddress }
): UpdateMetadataPointerInstruction<
  TProgramAddress,
  TAccountMint,
  (typeof input)['metadataPointerAuthority'] extends TransactionSigner<TAccountMetadataPointerAuthority>
    ? ReadonlySignerAccount<TAccountMetadataPointerAuthority> &
        AccountSignerMeta<TAccountMetadataPointerAuthority>
    : TAccountMetadataPointerAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    metadataPointerAuthority: {
      value: input.metadataPointerAuthority ?? null,
      isWritable: false,
    },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.metadataPointerAuthority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getUpdateMetadataPointerInstructionDataEncoder().encode(
      args as UpdateMetadataPointerInstructionDataArgs
    ),
  } as UpdateMetadataPointerInstruction<
    TProgramAddress,
    TAccountMint,
    (typeof input)['metadataPointerAuthority'] extends TransactionSigner<TAccountMetadataPointerAuthority>
      ? ReadonlySignerAccount<TAccountMetadataPointerAuthority> &
          AccountSignerMeta<TAccountMetadataPointerAuthority>
      : TAccountMetadataPointerAuthority
  >;

  return instruction;
}

export type ParsedUpdateMetadataPointerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint to initialize. */
    mint: TAccountMetas[0];
    /** The metadata pointer authority or its multisignature account. */
    metadataPointerAuthority: TAccountMetas[1];
  };
  data: UpdateMetadataPointerInstructionData;
};

export function parseUpdateMetadataPointerInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedUpdateMetadataPointerInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      metadataPointerAuthority: getNextAccount(),
    },
    data: getUpdateMetadataPointerInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

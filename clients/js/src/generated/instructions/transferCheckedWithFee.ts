/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR = 26;

export function getTransferCheckedWithFeeDiscriminatorBytes() {
  return getU8Encoder().encode(TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR);
}

export const TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR = 1;

export function getTransferCheckedWithFeeTransferFeeDiscriminatorBytes() {
  return getU8Encoder().encode(
    TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR
  );
}

export type TransferCheckedWithFeeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountSource extends string | AccountMeta<string> = string,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountDestination extends string | AccountMeta<string> = string,
  TAccountAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountSource extends string
        ? WritableAccount<TAccountSource>
        : TAccountSource,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountDestination extends string
        ? WritableAccount<TAccountDestination>
        : TAccountDestination,
      TAccountAuthority extends string
        ? ReadonlyAccount<TAccountAuthority>
        : TAccountAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type TransferCheckedWithFeeInstructionData = {
  discriminator: number;
  transferFeeDiscriminator: number;
  /** The amount of tokens to transfer. */
  amount: bigint;
  /** Expected number of base 10 digits to the right of the decimal place. */
  decimals: number;
  /**
   * Expected fee assessed on this transfer, calculated off-chain based
   * on the transfer_fee_basis_points and maximum_fee of the mint. May
   * be 0 for a mint without a configured transfer fee.
   */
  fee: bigint;
};

export type TransferCheckedWithFeeInstructionDataArgs = {
  /** The amount of tokens to transfer. */
  amount: number | bigint;
  /** Expected number of base 10 digits to the right of the decimal place. */
  decimals: number;
  /**
   * Expected fee assessed on this transfer, calculated off-chain based
   * on the transfer_fee_basis_points and maximum_fee of the mint. May
   * be 0 for a mint without a configured transfer fee.
   */
  fee: number | bigint;
};

export function getTransferCheckedWithFeeInstructionDataEncoder(): FixedSizeEncoder<TransferCheckedWithFeeInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['transferFeeDiscriminator', getU8Encoder()],
      ['amount', getU64Encoder()],
      ['decimals', getU8Encoder()],
      ['fee', getU64Encoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR,
      transferFeeDiscriminator:
        TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR,
    })
  );
}

export function getTransferCheckedWithFeeInstructionDataDecoder(): FixedSizeDecoder<TransferCheckedWithFeeInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['transferFeeDiscriminator', getU8Decoder()],
    ['amount', getU64Decoder()],
    ['decimals', getU8Decoder()],
    ['fee', getU64Decoder()],
  ]);
}

export function getTransferCheckedWithFeeInstructionDataCodec(): FixedSizeCodec<
  TransferCheckedWithFeeInstructionDataArgs,
  TransferCheckedWithFeeInstructionData
> {
  return combineCodec(
    getTransferCheckedWithFeeInstructionDataEncoder(),
    getTransferCheckedWithFeeInstructionDataDecoder()
  );
}

export type TransferCheckedWithFeeInput<
  TAccountSource extends string = string,
  TAccountMint extends string = string,
  TAccountDestination extends string = string,
  TAccountAuthority extends string = string,
> = {
  /** The source account. May include the `TransferFeeAmount` extension. */
  source: Address<TAccountSource>;
  /** The token mint. May include the `TransferFeeConfig` extension. */
  mint: Address<TAccountMint>;
  /** The destination account. May include the `TransferFeeAmount` extension. */
  destination: Address<TAccountDestination>;
  /** The source account's owner/delegate or its multisignature account. */
  authority: Address<TAccountAuthority> | TransactionSigner<TAccountAuthority>;
  amount: TransferCheckedWithFeeInstructionDataArgs['amount'];
  decimals: TransferCheckedWithFeeInstructionDataArgs['decimals'];
  fee: TransferCheckedWithFeeInstructionDataArgs['fee'];
  multiSigners?: Array<TransactionSigner>;
};

export function getTransferCheckedWithFeeInstruction<
  TAccountSource extends string,
  TAccountMint extends string,
  TAccountDestination extends string,
  TAccountAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: TransferCheckedWithFeeInput<
    TAccountSource,
    TAccountMint,
    TAccountDestination,
    TAccountAuthority
  >,
  config?: { programAddress?: TProgramAddress }
): TransferCheckedWithFeeInstruction<
  TProgramAddress,
  TAccountSource,
  TAccountMint,
  TAccountDestination,
  (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
    ? ReadonlySignerAccount<TAccountAuthority> &
        AccountSignerMeta<TAccountAuthority>
    : TAccountAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    destination: { value: input.destination ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.authority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getTransferCheckedWithFeeInstructionDataEncoder().encode(
      args as TransferCheckedWithFeeInstructionDataArgs
    ),
  } as TransferCheckedWithFeeInstruction<
    TProgramAddress,
    TAccountSource,
    TAccountMint,
    TAccountDestination,
    (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
      ? ReadonlySignerAccount<TAccountAuthority> &
          AccountSignerMeta<TAccountAuthority>
      : TAccountAuthority
  >;

  return instruction;
}

export type ParsedTransferCheckedWithFeeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The source account. May include the `TransferFeeAmount` extension. */
    source: TAccountMetas[0];
    /** The token mint. May include the `TransferFeeConfig` extension. */
    mint: TAccountMetas[1];
    /** The destination account. May include the `TransferFeeAmount` extension. */
    destination: TAccountMetas[2];
    /** The source account's owner/delegate or its multisignature account. */
    authority: TAccountMetas[3];
  };
  data: TransferCheckedWithFeeInstructionData;
};

export function parseTransferCheckedWithFeeInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedTransferCheckedWithFeeInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 4) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      mint: getNextAccount(),
      destination: getNextAccount(),
      authority: getNextAccount(),
    },
    data: getTransferCheckedWithFeeInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const APPROVE_CHECKED_DISCRIMINATOR = 13;

export function getApproveCheckedDiscriminatorBytes() {
  return getU8Encoder().encode(APPROVE_CHECKED_DISCRIMINATOR);
}

export type ApproveCheckedInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountSource extends string | AccountMeta<string> = string,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountDelegate extends string | AccountMeta<string> = string,
  TAccountOwner extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountSource extends string
        ? WritableAccount<TAccountSource>
        : TAccountSource,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountDelegate extends string
        ? ReadonlyAccount<TAccountDelegate>
        : TAccountDelegate,
      TAccountOwner extends string
        ? ReadonlyAccount<TAccountOwner>
        : TAccountOwner,
      ...TRemainingAccounts,
    ]
  >;

export type ApproveCheckedInstructionData = {
  discriminator: number;
  /** The amount of tokens the delegate is approved for. */
  amount: bigint;
  /** Expected number of base 10 digits to the right of the decimal place. */
  decimals: number;
};

export type ApproveCheckedInstructionDataArgs = {
  /** The amount of tokens the delegate is approved for. */
  amount: number | bigint;
  /** Expected number of base 10 digits to the right of the decimal place. */
  decimals: number;
};

export function getApproveCheckedInstructionDataEncoder(): FixedSizeEncoder<ApproveCheckedInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['amount', getU64Encoder()],
      ['decimals', getU8Encoder()],
    ]),
    (value) => ({ ...value, discriminator: APPROVE_CHECKED_DISCRIMINATOR })
  );
}

export function getApproveCheckedInstructionDataDecoder(): FixedSizeDecoder<ApproveCheckedInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['amount', getU64Decoder()],
    ['decimals', getU8Decoder()],
  ]);
}

export function getApproveCheckedInstructionDataCodec(): FixedSizeCodec<
  ApproveCheckedInstructionDataArgs,
  ApproveCheckedInstructionData
> {
  return combineCodec(
    getApproveCheckedInstructionDataEncoder(),
    getApproveCheckedInstructionDataDecoder()
  );
}

export type ApproveCheckedInput<
  TAccountSource extends string = string,
  TAccountMint extends string = string,
  TAccountDelegate extends string = string,
  TAccountOwner extends string = string,
> = {
  /** The source account. */
  source: Address<TAccountSource>;
  /** The token mint. */
  mint: Address<TAccountMint>;
  /** The delegate. */
  delegate: Address<TAccountDelegate>;
  /** The source account owner or its multisignature account. */
  owner: Address<TAccountOwner> | TransactionSigner<TAccountOwner>;
  amount: ApproveCheckedInstructionDataArgs['amount'];
  decimals: ApproveCheckedInstructionDataArgs['decimals'];
  multiSigners?: Array<TransactionSigner>;
};

export function getApproveCheckedInstruction<
  TAccountSource extends string,
  TAccountMint extends string,
  TAccountDelegate extends string,
  TAccountOwner extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: ApproveCheckedInput<
    TAccountSource,
    TAccountMint,
    TAccountDelegate,
    TAccountOwner
  >,
  config?: { programAddress?: TProgramAddress }
): ApproveCheckedInstruction<
  TProgramAddress,
  TAccountSource,
  TAccountMint,
  TAccountDelegate,
  (typeof input)['owner'] extends TransactionSigner<TAccountOwner>
    ? ReadonlySignerAccount<TAccountOwner> & AccountSignerMeta<TAccountOwner>
    : TAccountOwner
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    delegate: { value: input.delegate ?? null, isWritable: false },
    owner: { value: input.owner ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.delegate),
      getAccountMeta(accounts.owner),
      ...remainingAccounts,
    ],
    programAddress,
    data: getApproveCheckedInstructionDataEncoder().encode(
      args as ApproveCheckedInstructionDataArgs
    ),
  } as ApproveCheckedInstruction<
    TProgramAddress,
    TAccountSource,
    TAccountMint,
    TAccountDelegate,
    (typeof input)['owner'] extends TransactionSigner<TAccountOwner>
      ? ReadonlySignerAccount<TAccountOwner> & AccountSignerMeta<TAccountOwner>
      : TAccountOwner
  >;

  return instruction;
}

export type ParsedApproveCheckedInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The source account. */
    source: TAccountMetas[0];
    /** The token mint. */
    mint: TAccountMetas[1];
    /** The delegate. */
    delegate: TAccountMetas[2];
    /** The source account owner or its multisignature account. */
    owner: TAccountMetas[3];
  };
  data: ApproveCheckedInstructionData;
};

export function parseApproveCheckedInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedApproveCheckedInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 4) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      mint: getNextAccount(),
      delegate: getNextAccount(),
      owner: getNextAccount(),
    },
    data: getApproveCheckedInstructionDataDecoder().decode(instruction.data),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';
import {
  getAccountStateDecoder,
  getAccountStateEncoder,
  type AccountState,
  type AccountStateArgs,
} from '../types';

export const INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = 28;

export function getInitializeDefaultAccountStateDiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR);
}

export const INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = 0;

export function getInitializeDefaultAccountStateDefaultAccountStateDiscriminatorBytes() {
  return getU8Encoder().encode(
    INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR
  );
}

export type InitializeDefaultAccountStateInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeDefaultAccountStateInstructionData = {
  discriminator: number;
  defaultAccountStateDiscriminator: number;
  /** The state each new token account should start with. */
  state: AccountState;
};

export type InitializeDefaultAccountStateInstructionDataArgs = {
  /** The state each new token account should start with. */
  state: AccountStateArgs;
};

export function getInitializeDefaultAccountStateInstructionDataEncoder(): FixedSizeEncoder<InitializeDefaultAccountStateInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['defaultAccountStateDiscriminator', getU8Encoder()],
      ['state', getAccountStateEncoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR,
      defaultAccountStateDiscriminator:
        INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR,
    })
  );
}

export function getInitializeDefaultAccountStateInstructionDataDecoder(): FixedSizeDecoder<InitializeDefaultAccountStateInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['defaultAccountStateDiscriminator', getU8Decoder()],
    ['state', getAccountStateDecoder()],
  ]);
}

export function getInitializeDefaultAccountStateInstructionDataCodec(): FixedSizeCodec<
  InitializeDefaultAccountStateInstructionDataArgs,
  InitializeDefaultAccountStateInstructionData
> {
  return combineCodec(
    getInitializeDefaultAccountStateInstructionDataEncoder(),
    getInitializeDefaultAccountStateInstructionDataDecoder()
  );
}

export type InitializeDefaultAccountStateInput<
  TAccountMint extends string = string,
> = {
  /** The mint. */
  mint: Address<TAccountMint>;
  state: InitializeDefaultAccountStateInstructionDataArgs['state'];
};

export function getInitializeDefaultAccountStateInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeDefaultAccountStateInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): InitializeDefaultAccountStateInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeDefaultAccountStateInstructionDataEncoder().encode(
      args as InitializeDefaultAccountStateInstructionDataArgs
    ),
  } as InitializeDefaultAccountStateInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedInitializeDefaultAccountStateInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint. */
    mint: TAccountMetas[0];
  };
  data: InitializeDefaultAccountStateInstructionData;
};

export function parseInitializeDefaultAccountStateInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeDefaultAccountStateInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getInitializeDefaultAccountStateInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR = 36;

export function getInitializeTransferHookDiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR);
}

export const INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR = 0;

export function getInitializeTransferHookTransferHookDiscriminatorBytes() {
  return getU8Encoder().encode(
    INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR
  );
}

export type InitializeTransferHookInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeTransferHookInstructionData = {
  discriminator: number;
  transferHookDiscriminator: number;
  /** The public key for the account that can update the program id */
  authority: Option<Address>;
  /** The program id that performs logic during transfers */
  programId: Option<Address>;
};

export type InitializeTransferHookInstructionDataArgs = {
  /** The public key for the account that can update the program id */
  authority: OptionOrNullable<Address>;
  /** The program id that performs logic during transfers */
  programId: OptionOrNullable<Address>;
};

export function getInitializeTransferHookInstructionDataEncoder(): FixedSizeEncoder<InitializeTransferHookInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['transferHookDiscriminator', getU8Encoder()],
      [
        'authority',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
      [
        'programId',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR,
      transferHookDiscriminator:
        INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR,
    })
  );
}

export function getInitializeTransferHookInstructionDataDecoder(): FixedSizeDecoder<InitializeTransferHookInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['transferHookDiscriminator', getU8Decoder()],
    [
      'authority',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
    [
      'programId',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
  ]);
}

export function getInitializeTransferHookInstructionDataCodec(): FixedSizeCodec<
  InitializeTransferHookInstructionDataArgs,
  InitializeTransferHookInstructionData
> {
  return combineCodec(
    getInitializeTransferHookInstructionDataEncoder(),
    getInitializeTransferHookInstructionDataDecoder()
  );
}

export type InitializeTransferHookInput<TAccountMint extends string = string> =
  {
    /** The mint to initialize. */
    mint: Address<TAccountMint>;
    authority: InitializeTransferHookInstructionDataArgs['authority'];
    programId: InitializeTransferHookInstructionDataArgs['programId'];
  };

export function getInitializeTransferHookInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeTransferHookInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): InitializeTransferHookInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeTransferHookInstructionDataEncoder().encode(
      args as InitializeTransferHookInstructionDataArgs
    ),
  } as InitializeTransferHookInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedInitializeTransferHookInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint to initialize. */
    mint: TAccountMetas[0];
  };
  data: InitializeTransferHookInstructionData;
};

export function parseInitializeTransferHookInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeTransferHookInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getInitializeTransferHookInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

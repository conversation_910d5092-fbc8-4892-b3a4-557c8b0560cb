/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR = 22;

export function getInitializeImmutableOwnerDiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR);
}

export type InitializeImmutableOwnerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountAccount extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountAccount extends string
        ? WritableAccount<TAccountAccount>
        : TAccountAccount,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeImmutableOwnerInstructionData = { discriminator: number };

export type InitializeImmutableOwnerInstructionDataArgs = {};

export function getInitializeImmutableOwnerInstructionDataEncoder(): FixedSizeEncoder<InitializeImmutableOwnerInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([['discriminator', getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR,
    })
  );
}

export function getInitializeImmutableOwnerInstructionDataDecoder(): FixedSizeDecoder<InitializeImmutableOwnerInstructionData> {
  return getStructDecoder([['discriminator', getU8Decoder()]]);
}

export function getInitializeImmutableOwnerInstructionDataCodec(): FixedSizeCodec<
  InitializeImmutableOwnerInstructionDataArgs,
  InitializeImmutableOwnerInstructionData
> {
  return combineCodec(
    getInitializeImmutableOwnerInstructionDataEncoder(),
    getInitializeImmutableOwnerInstructionDataDecoder()
  );
}

export type InitializeImmutableOwnerInput<
  TAccountAccount extends string = string,
> = {
  /** The account to initialize. */
  account: Address<TAccountAccount>;
};

export function getInitializeImmutableOwnerInstruction<
  TAccountAccount extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeImmutableOwnerInput<TAccountAccount>,
  config?: { programAddress?: TProgramAddress }
): InitializeImmutableOwnerInstruction<TProgramAddress, TAccountAccount> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.account)],
    programAddress,
    data: getInitializeImmutableOwnerInstructionDataEncoder().encode({}),
  } as InitializeImmutableOwnerInstruction<TProgramAddress, TAccountAccount>;

  return instruction;
}

export type ParsedInitializeImmutableOwnerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The account to initialize. */
    account: TAccountMetas[0];
  };
  data: InitializeImmutableOwnerInstructionData;
};

export function parseInitializeImmutableOwnerInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeImmutableOwnerInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
    },
    data: getInitializeImmutableOwnerInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_MULTISIG2_DISCRIMINATOR = 19;

export function getInitializeMultisig2DiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_MULTISIG2_DISCRIMINATOR);
}

export type InitializeMultisig2Instruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMultisig extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMultisig extends string
        ? WritableAccount<TAccountMultisig>
        : TAccountMultisig,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeMultisig2InstructionData = {
  discriminator: number;
  /** The number of signers (M) required to validate this multisignature account. */
  m: number;
};

export type InitializeMultisig2InstructionDataArgs = {
  /** The number of signers (M) required to validate this multisignature account. */
  m: number;
};

export function getInitializeMultisig2InstructionDataEncoder(): FixedSizeEncoder<InitializeMultisig2InstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['m', getU8Encoder()],
    ]),
    (value) => ({ ...value, discriminator: INITIALIZE_MULTISIG2_DISCRIMINATOR })
  );
}

export function getInitializeMultisig2InstructionDataDecoder(): FixedSizeDecoder<InitializeMultisig2InstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['m', getU8Decoder()],
  ]);
}

export function getInitializeMultisig2InstructionDataCodec(): FixedSizeCodec<
  InitializeMultisig2InstructionDataArgs,
  InitializeMultisig2InstructionData
> {
  return combineCodec(
    getInitializeMultisig2InstructionDataEncoder(),
    getInitializeMultisig2InstructionDataDecoder()
  );
}

export type InitializeMultisig2Input<TAccountMultisig extends string = string> =
  {
    /** The multisignature account to initialize. */
    multisig: Address<TAccountMultisig>;
    m: InitializeMultisig2InstructionDataArgs['m'];
    signers: Array<Address>;
  };

export function getInitializeMultisig2Instruction<
  TAccountMultisig extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeMultisig2Input<TAccountMultisig>,
  config?: { programAddress?: TProgramAddress }
): InitializeMultisig2Instruction<TProgramAddress, TAccountMultisig> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    multisig: { value: input.multisig ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = args.signers.map((address) => ({
    address,
    role: AccountRole.READONLY,
  }));

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.multisig), ...remainingAccounts],
    programAddress,
    data: getInitializeMultisig2InstructionDataEncoder().encode(
      args as InitializeMultisig2InstructionDataArgs
    ),
  } as InitializeMultisig2Instruction<TProgramAddress, TAccountMultisig>;

  return instruction;
}

export type ParsedInitializeMultisig2Instruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The multisignature account to initialize. */
    multisig: TAccountMetas[0];
  };
  data: InitializeMultisig2InstructionData;
};

export function parseInitializeMultisig2Instruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeMultisig2Instruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      multisig: getNextAccount(),
    },
    data: getInitializeMultisig2InstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

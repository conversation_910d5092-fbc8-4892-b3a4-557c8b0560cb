/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR = 44;

export function getInitializePausableConfigDiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR);
}

export const INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR = 0;

export function getInitializePausableConfigPausableDiscriminatorBytes() {
  return getU8Encoder().encode(
    INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR
  );
}

export type InitializePausableConfigInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type InitializePausableConfigInstructionData = {
  discriminator: number;
  pausableDiscriminator: number;
  /** The authority that can pause and resume the mint. */
  authority: Option<Address>;
};

export type InitializePausableConfigInstructionDataArgs = {
  /** The authority that can pause and resume the mint. */
  authority: OptionOrNullable<Address>;
};

export function getInitializePausableConfigInstructionDataEncoder(): FixedSizeEncoder<InitializePausableConfigInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['pausableDiscriminator', getU8Encoder()],
      [
        'authority',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR,
      pausableDiscriminator: INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR,
    })
  );
}

export function getInitializePausableConfigInstructionDataDecoder(): FixedSizeDecoder<InitializePausableConfigInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['pausableDiscriminator', getU8Decoder()],
    [
      'authority',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
  ]);
}

export function getInitializePausableConfigInstructionDataCodec(): FixedSizeCodec<
  InitializePausableConfigInstructionDataArgs,
  InitializePausableConfigInstructionData
> {
  return combineCodec(
    getInitializePausableConfigInstructionDataEncoder(),
    getInitializePausableConfigInstructionDataDecoder()
  );
}

export type InitializePausableConfigInput<
  TAccountMint extends string = string,
> = {
  /** The mint. */
  mint: Address<TAccountMint>;
  authority: InitializePausableConfigInstructionDataArgs['authority'];
};

export function getInitializePausableConfigInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializePausableConfigInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): InitializePausableConfigInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializePausableConfigInstructionDataEncoder().encode(
      args as InitializePausableConfigInstructionDataArgs
    ),
  } as InitializePausableConfigInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedInitializePausableConfigInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint. */
    mint: TAccountMetas[0];
  };
  data: InitializePausableConfigInstructionData;
};

export function parseInitializePausableConfigInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializePausableConfigInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getInitializePausableConfigInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

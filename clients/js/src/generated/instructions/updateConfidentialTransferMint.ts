/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getBooleanDecoder,
  getBooleanEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR = 27;

export function getUpdateConfidentialTransferMintDiscriminatorBytes() {
  return getU8Encoder().encode(UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR);
}

export const UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 1;

export function getUpdateConfidentialTransferMintConfidentialTransferDiscriminatorBytes() {
  return getU8Encoder().encode(
    UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}

export type UpdateConfidentialTransferMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountAuthority extends string
        ? ReadonlySignerAccount<TAccountAuthority> &
            AccountSignerMeta<TAccountAuthority>
        : TAccountAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type UpdateConfidentialTransferMintInstructionData = {
  discriminator: number;
  confidentialTransferDiscriminator: number;
  /**
   * Determines if newly configured accounts must be approved by the
   * `authority` before they may be used by the user.
   */
  autoApproveNewAccounts: boolean;
  /** New authority to decode any transfer amount in a confidential transfer. */
  auditorElgamalPubkey: Option<Address>;
};

export type UpdateConfidentialTransferMintInstructionDataArgs = {
  /**
   * Determines if newly configured accounts must be approved by the
   * `authority` before they may be used by the user.
   */
  autoApproveNewAccounts: boolean;
  /** New authority to decode any transfer amount in a confidential transfer. */
  auditorElgamalPubkey: OptionOrNullable<Address>;
};

export function getUpdateConfidentialTransferMintInstructionDataEncoder(): FixedSizeEncoder<UpdateConfidentialTransferMintInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['confidentialTransferDiscriminator', getU8Encoder()],
      ['autoApproveNewAccounts', getBooleanEncoder()],
      [
        'auditorElgamalPubkey',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR,
      confidentialTransferDiscriminator:
        UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR,
    })
  );
}

export function getUpdateConfidentialTransferMintInstructionDataDecoder(): FixedSizeDecoder<UpdateConfidentialTransferMintInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['confidentialTransferDiscriminator', getU8Decoder()],
    ['autoApproveNewAccounts', getBooleanDecoder()],
    [
      'auditorElgamalPubkey',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
  ]);
}

export function getUpdateConfidentialTransferMintInstructionDataCodec(): FixedSizeCodec<
  UpdateConfidentialTransferMintInstructionDataArgs,
  UpdateConfidentialTransferMintInstructionData
> {
  return combineCodec(
    getUpdateConfidentialTransferMintInstructionDataEncoder(),
    getUpdateConfidentialTransferMintInstructionDataDecoder()
  );
}

export type UpdateConfidentialTransferMintInput<
  TAccountMint extends string = string,
  TAccountAuthority extends string = string,
> = {
  /** The SPL Token mint. */
  mint: Address<TAccountMint>;
  /** Confidential transfer mint authority. */
  authority: TransactionSigner<TAccountAuthority>;
  autoApproveNewAccounts: UpdateConfidentialTransferMintInstructionDataArgs['autoApproveNewAccounts'];
  auditorElgamalPubkey: UpdateConfidentialTransferMintInstructionDataArgs['auditorElgamalPubkey'];
};

export function getUpdateConfidentialTransferMintInstruction<
  TAccountMint extends string,
  TAccountAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: UpdateConfidentialTransferMintInput<TAccountMint, TAccountAuthority>,
  config?: { programAddress?: TProgramAddress }
): UpdateConfidentialTransferMintInstruction<
  TProgramAddress,
  TAccountMint,
  TAccountAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
    ],
    programAddress,
    data: getUpdateConfidentialTransferMintInstructionDataEncoder().encode(
      args as UpdateConfidentialTransferMintInstructionDataArgs
    ),
  } as UpdateConfidentialTransferMintInstruction<
    TProgramAddress,
    TAccountMint,
    TAccountAuthority
  >;

  return instruction;
}

export type ParsedUpdateConfidentialTransferMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The SPL Token mint. */
    mint: TAccountMetas[0];
    /** Confidential transfer mint authority. */
    authority: TAccountMetas[1];
  };
  data: UpdateConfidentialTransferMintInstructionData;
};

export function parseUpdateConfidentialTransferMintInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedUpdateConfidentialTransferMintInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount(),
    },
    data: getUpdateConfidentialTransferMintInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

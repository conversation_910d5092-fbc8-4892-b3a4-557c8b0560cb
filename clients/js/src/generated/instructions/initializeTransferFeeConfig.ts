/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU16Decoder,
  getU16Encoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR = 26;

export function getInitializeTransferFeeConfigDiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR);
}

export const INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR = 0;

export function getInitializeTransferFeeConfigTransferFeeDiscriminatorBytes() {
  return getU8Encoder().encode(
    INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR
  );
}

export type InitializeTransferFeeConfigInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeTransferFeeConfigInstructionData = {
  discriminator: number;
  transferFeeDiscriminator: number;
  /** Pubkey that may update the fees. */
  transferFeeConfigAuthority: Option<Address>;
  /** Withdraw instructions must be signed by this key. */
  withdrawWithheldAuthority: Option<Address>;
  /** Amount of transfer collected as fees, expressed as basis points of the transfer amount. */
  transferFeeBasisPoints: number;
  /** Maximum fee assessed on transfers. */
  maximumFee: bigint;
};

export type InitializeTransferFeeConfigInstructionDataArgs = {
  /** Pubkey that may update the fees. */
  transferFeeConfigAuthority: OptionOrNullable<Address>;
  /** Withdraw instructions must be signed by this key. */
  withdrawWithheldAuthority: OptionOrNullable<Address>;
  /** Amount of transfer collected as fees, expressed as basis points of the transfer amount. */
  transferFeeBasisPoints: number;
  /** Maximum fee assessed on transfers. */
  maximumFee: number | bigint;
};

export function getInitializeTransferFeeConfigInstructionDataEncoder(): Encoder<InitializeTransferFeeConfigInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['transferFeeDiscriminator', getU8Encoder()],
      ['transferFeeConfigAuthority', getOptionEncoder(getAddressEncoder())],
      ['withdrawWithheldAuthority', getOptionEncoder(getAddressEncoder())],
      ['transferFeeBasisPoints', getU16Encoder()],
      ['maximumFee', getU64Encoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR,
      transferFeeDiscriminator:
        INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR,
    })
  );
}

export function getInitializeTransferFeeConfigInstructionDataDecoder(): Decoder<InitializeTransferFeeConfigInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['transferFeeDiscriminator', getU8Decoder()],
    ['transferFeeConfigAuthority', getOptionDecoder(getAddressDecoder())],
    ['withdrawWithheldAuthority', getOptionDecoder(getAddressDecoder())],
    ['transferFeeBasisPoints', getU16Decoder()],
    ['maximumFee', getU64Decoder()],
  ]);
}

export function getInitializeTransferFeeConfigInstructionDataCodec(): Codec<
  InitializeTransferFeeConfigInstructionDataArgs,
  InitializeTransferFeeConfigInstructionData
> {
  return combineCodec(
    getInitializeTransferFeeConfigInstructionDataEncoder(),
    getInitializeTransferFeeConfigInstructionDataDecoder()
  );
}

export type InitializeTransferFeeConfigInput<
  TAccountMint extends string = string,
> = {
  /** The mint to initialize. */
  mint: Address<TAccountMint>;
  transferFeeConfigAuthority: InitializeTransferFeeConfigInstructionDataArgs['transferFeeConfigAuthority'];
  withdrawWithheldAuthority: InitializeTransferFeeConfigInstructionDataArgs['withdrawWithheldAuthority'];
  transferFeeBasisPoints: InitializeTransferFeeConfigInstructionDataArgs['transferFeeBasisPoints'];
  maximumFee: InitializeTransferFeeConfigInstructionDataArgs['maximumFee'];
};

export function getInitializeTransferFeeConfigInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeTransferFeeConfigInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): InitializeTransferFeeConfigInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeTransferFeeConfigInstructionDataEncoder().encode(
      args as InitializeTransferFeeConfigInstructionDataArgs
    ),
  } as InitializeTransferFeeConfigInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedInitializeTransferFeeConfigInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint to initialize. */
    mint: TAccountMetas[0];
  };
  data: InitializeTransferFeeConfigInstructionData;
};

export function parseInitializeTransferFeeConfigInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeTransferFeeConfigInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getInitializeTransferFeeConfigInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

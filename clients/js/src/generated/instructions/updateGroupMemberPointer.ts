/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR = 41;

export function getUpdateGroupMemberPointerDiscriminatorBytes() {
  return getU8Encoder().encode(UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR);
}

export const UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR = 1;

export function getUpdateGroupMemberPointerGroupMemberPointerDiscriminatorBytes() {
  return getU8Encoder().encode(
    UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR
  );
}

export type UpdateGroupMemberPointerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountGroupMemberPointerAuthority extends
    | string
    | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      TAccountGroupMemberPointerAuthority extends string
        ? ReadonlyAccount<TAccountGroupMemberPointerAuthority>
        : TAccountGroupMemberPointerAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type UpdateGroupMemberPointerInstructionData = {
  discriminator: number;
  groupMemberPointerDiscriminator: number;
  /** The new account address that holds the member. */
  memberAddress: Option<Address>;
};

export type UpdateGroupMemberPointerInstructionDataArgs = {
  /** The new account address that holds the member. */
  memberAddress: OptionOrNullable<Address>;
};

export function getUpdateGroupMemberPointerInstructionDataEncoder(): FixedSizeEncoder<UpdateGroupMemberPointerInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['groupMemberPointerDiscriminator', getU8Encoder()],
      [
        'memberAddress',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR,
      groupMemberPointerDiscriminator:
        UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR,
    })
  );
}

export function getUpdateGroupMemberPointerInstructionDataDecoder(): FixedSizeDecoder<UpdateGroupMemberPointerInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['groupMemberPointerDiscriminator', getU8Decoder()],
    [
      'memberAddress',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
  ]);
}

export function getUpdateGroupMemberPointerInstructionDataCodec(): FixedSizeCodec<
  UpdateGroupMemberPointerInstructionDataArgs,
  UpdateGroupMemberPointerInstructionData
> {
  return combineCodec(
    getUpdateGroupMemberPointerInstructionDataEncoder(),
    getUpdateGroupMemberPointerInstructionDataDecoder()
  );
}

export type UpdateGroupMemberPointerInput<
  TAccountMint extends string = string,
  TAccountGroupMemberPointerAuthority extends string = string,
> = {
  /** The mint to initialize. */
  mint: Address<TAccountMint>;
  /** The group member pointer authority or its multisignature account. */
  groupMemberPointerAuthority:
    | Address<TAccountGroupMemberPointerAuthority>
    | TransactionSigner<TAccountGroupMemberPointerAuthority>;
  memberAddress: UpdateGroupMemberPointerInstructionDataArgs['memberAddress'];
  multiSigners?: Array<TransactionSigner>;
};

export function getUpdateGroupMemberPointerInstruction<
  TAccountMint extends string,
  TAccountGroupMemberPointerAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: UpdateGroupMemberPointerInput<
    TAccountMint,
    TAccountGroupMemberPointerAuthority
  >,
  config?: { programAddress?: TProgramAddress }
): UpdateGroupMemberPointerInstruction<
  TProgramAddress,
  TAccountMint,
  (typeof input)['groupMemberPointerAuthority'] extends TransactionSigner<TAccountGroupMemberPointerAuthority>
    ? ReadonlySignerAccount<TAccountGroupMemberPointerAuthority> &
        AccountSignerMeta<TAccountGroupMemberPointerAuthority>
    : TAccountGroupMemberPointerAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    groupMemberPointerAuthority: {
      value: input.groupMemberPointerAuthority ?? null,
      isWritable: false,
    },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.groupMemberPointerAuthority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getUpdateGroupMemberPointerInstructionDataEncoder().encode(
      args as UpdateGroupMemberPointerInstructionDataArgs
    ),
  } as UpdateGroupMemberPointerInstruction<
    TProgramAddress,
    TAccountMint,
    (typeof input)['groupMemberPointerAuthority'] extends TransactionSigner<TAccountGroupMemberPointerAuthority>
      ? ReadonlySignerAccount<TAccountGroupMemberPointerAuthority> &
          AccountSignerMeta<TAccountGroupMemberPointerAuthority>
      : TAccountGroupMemberPointerAuthority
  >;

  return instruction;
}

export type ParsedUpdateGroupMemberPointerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint to initialize. */
    mint: TAccountMetas[0];
    /** The group member pointer authority or its multisignature account. */
    groupMemberPointerAuthority: TAccountMetas[1];
  };
  data: UpdateGroupMemberPointerInstructionData;
};

export function parseUpdateGroupMemberPointerInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedUpdateGroupMemberPointerInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      groupMemberPointerAuthority: getNextAccount(),
    },
    data: getUpdateGroupMemberPointerInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

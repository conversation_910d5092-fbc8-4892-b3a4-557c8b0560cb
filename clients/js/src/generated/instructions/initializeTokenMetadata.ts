/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  addDecoderSizePrefix,
  addEncoderSizePrefix,
  combineCodec,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU32Decoder,
  getU32Encoder,
  getUtf8Decoder,
  getUtf8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_TOKEN_METADATA_DISCRIMINATOR = new Uint8Array([
  210, 225, 30, 162, 88, 184, 77, 141,
]);

export function getInitializeTokenMetadataDiscriminatorBytes() {
  return getBytesEncoder().encode(INITIALIZE_TOKEN_METADATA_DISCRIMINATOR);
}

export type InitializeTokenMetadataInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetadata extends string | AccountMeta<string> = string,
  TAccountUpdateAuthority extends string | AccountMeta<string> = string,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountMintAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMetadata extends string
        ? WritableAccount<TAccountMetadata>
        : TAccountMetadata,
      TAccountUpdateAuthority extends string
        ? ReadonlyAccount<TAccountUpdateAuthority>
        : TAccountUpdateAuthority,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountMintAuthority extends string
        ? ReadonlySignerAccount<TAccountMintAuthority> &
            AccountSignerMeta<TAccountMintAuthority>
        : TAccountMintAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeTokenMetadataInstructionData = {
  discriminator: ReadonlyUint8Array;
  /** Longer name of the token. */
  name: string;
  /** Shortened symbol of the token. */
  symbol: string;
  /** URI pointing to more metadata (image, video, etc.). */
  uri: string;
};

export type InitializeTokenMetadataInstructionDataArgs = {
  /** Longer name of the token. */
  name: string;
  /** Shortened symbol of the token. */
  symbol: string;
  /** URI pointing to more metadata (image, video, etc.). */
  uri: string;
};

export function getInitializeTokenMetadataInstructionDataEncoder(): Encoder<InitializeTokenMetadataInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getBytesEncoder()],
      ['name', addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder())],
      ['symbol', addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder())],
      ['uri', addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder())],
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TOKEN_METADATA_DISCRIMINATOR,
    })
  );
}

export function getInitializeTokenMetadataInstructionDataDecoder(): Decoder<InitializeTokenMetadataInstructionData> {
  return getStructDecoder([
    ['discriminator', getBytesDecoder()],
    ['name', addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())],
    ['symbol', addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())],
    ['uri', addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())],
  ]);
}

export function getInitializeTokenMetadataInstructionDataCodec(): Codec<
  InitializeTokenMetadataInstructionDataArgs,
  InitializeTokenMetadataInstructionData
> {
  return combineCodec(
    getInitializeTokenMetadataInstructionDataEncoder(),
    getInitializeTokenMetadataInstructionDataDecoder()
  );
}

export type InitializeTokenMetadataInput<
  TAccountMetadata extends string = string,
  TAccountUpdateAuthority extends string = string,
  TAccountMint extends string = string,
  TAccountMintAuthority extends string = string,
> = {
  metadata: Address<TAccountMetadata>;
  updateAuthority: Address<TAccountUpdateAuthority>;
  mint: Address<TAccountMint>;
  mintAuthority: TransactionSigner<TAccountMintAuthority>;
  name: InitializeTokenMetadataInstructionDataArgs['name'];
  symbol: InitializeTokenMetadataInstructionDataArgs['symbol'];
  uri: InitializeTokenMetadataInstructionDataArgs['uri'];
};

export function getInitializeTokenMetadataInstruction<
  TAccountMetadata extends string,
  TAccountUpdateAuthority extends string,
  TAccountMint extends string,
  TAccountMintAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeTokenMetadataInput<
    TAccountMetadata,
    TAccountUpdateAuthority,
    TAccountMint,
    TAccountMintAuthority
  >,
  config?: { programAddress?: TProgramAddress }
): InitializeTokenMetadataInstruction<
  TProgramAddress,
  TAccountMetadata,
  TAccountUpdateAuthority,
  TAccountMint,
  TAccountMintAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false,
    },
    mint: { value: input.mint ?? null, isWritable: false },
    mintAuthority: { value: input.mintAuthority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.metadata),
      getAccountMeta(accounts.updateAuthority),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.mintAuthority),
    ],
    programAddress,
    data: getInitializeTokenMetadataInstructionDataEncoder().encode(
      args as InitializeTokenMetadataInstructionDataArgs
    ),
  } as InitializeTokenMetadataInstruction<
    TProgramAddress,
    TAccountMetadata,
    TAccountUpdateAuthority,
    TAccountMint,
    TAccountMintAuthority
  >;

  return instruction;
}

export type ParsedInitializeTokenMetadataInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    metadata: TAccountMetas[0];
    updateAuthority: TAccountMetas[1];
    mint: TAccountMetas[2];
    mintAuthority: TAccountMetas[3];
  };
  data: InitializeTokenMetadataInstructionData;
};

export function parseInitializeTokenMetadataInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeTokenMetadataInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 4) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount(),
      updateAuthority: getNextAccount(),
      mint: getNextAccount(),
      mintAuthority: getNextAccount(),
    },
    data: getInitializeTokenMetadataInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

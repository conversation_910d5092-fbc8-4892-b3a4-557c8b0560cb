/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR = 26;

export function getHarvestWithheldTokensToMintDiscriminatorBytes() {
  return getU8Encoder().encode(HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR);
}

export const HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR = 4;

export function getHarvestWithheldTokensToMintTransferFeeDiscriminatorBytes() {
  return getU8Encoder().encode(
    HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR
  );
}

export type HarvestWithheldTokensToMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type HarvestWithheldTokensToMintInstructionData = {
  discriminator: number;
  transferFeeDiscriminator: number;
};

export type HarvestWithheldTokensToMintInstructionDataArgs = {};

export function getHarvestWithheldTokensToMintInstructionDataEncoder(): FixedSizeEncoder<HarvestWithheldTokensToMintInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['transferFeeDiscriminator', getU8Encoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR,
      transferFeeDiscriminator:
        HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR,
    })
  );
}

export function getHarvestWithheldTokensToMintInstructionDataDecoder(): FixedSizeDecoder<HarvestWithheldTokensToMintInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['transferFeeDiscriminator', getU8Decoder()],
  ]);
}

export function getHarvestWithheldTokensToMintInstructionDataCodec(): FixedSizeCodec<
  HarvestWithheldTokensToMintInstructionDataArgs,
  HarvestWithheldTokensToMintInstructionData
> {
  return combineCodec(
    getHarvestWithheldTokensToMintInstructionDataEncoder(),
    getHarvestWithheldTokensToMintInstructionDataDecoder()
  );
}

export type HarvestWithheldTokensToMintInput<
  TAccountMint extends string = string,
> = {
  /** The token mint. */
  mint: Address<TAccountMint>;
  sources: Array<Address>;
};

export function getHarvestWithheldTokensToMintInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: HarvestWithheldTokensToMintInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): HarvestWithheldTokensToMintInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = args.sources.map((address) => ({
    address,
    role: AccountRole.WRITABLE,
  }));

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint), ...remainingAccounts],
    programAddress,
    data: getHarvestWithheldTokensToMintInstructionDataEncoder().encode({}),
  } as HarvestWithheldTokensToMintInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedHarvestWithheldTokensToMintInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The token mint. */
    mint: TAccountMetas[0];
  };
  data: HarvestWithheldTokensToMintInstructionData;
};

export function parseHarvestWithheldTokensToMintInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedHarvestWithheldTokensToMintInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getHarvestWithheldTokensToMintInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

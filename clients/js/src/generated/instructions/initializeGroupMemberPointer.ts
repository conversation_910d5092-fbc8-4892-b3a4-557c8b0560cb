/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR = 41;

export function getInitializeGroupMemberPointerDiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR);
}

export const INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR = 0;

export function getInitializeGroupMemberPointerGroupMemberPointerDiscriminatorBytes() {
  return getU8Encoder().encode(
    INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR
  );
}

export type InitializeGroupMemberPointerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeGroupMemberPointerInstructionData = {
  discriminator: number;
  groupMemberPointerDiscriminator: number;
  /** The public key for the account that can update the group member address. */
  authority: Option<Address>;
  /** The account address that holds the member. */
  memberAddress: Option<Address>;
};

export type InitializeGroupMemberPointerInstructionDataArgs = {
  /** The public key for the account that can update the group member address. */
  authority: OptionOrNullable<Address>;
  /** The account address that holds the member. */
  memberAddress: OptionOrNullable<Address>;
};

export function getInitializeGroupMemberPointerInstructionDataEncoder(): FixedSizeEncoder<InitializeGroupMemberPointerInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['groupMemberPointerDiscriminator', getU8Encoder()],
      [
        'authority',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
      [
        'memberAddress',
        getOptionEncoder(getAddressEncoder(), {
          prefix: null,
          noneValue: 'zeroes',
        }),
      ],
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR,
      groupMemberPointerDiscriminator:
        INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR,
    })
  );
}

export function getInitializeGroupMemberPointerInstructionDataDecoder(): FixedSizeDecoder<InitializeGroupMemberPointerInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['groupMemberPointerDiscriminator', getU8Decoder()],
    [
      'authority',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
    [
      'memberAddress',
      getOptionDecoder(getAddressDecoder(), {
        prefix: null,
        noneValue: 'zeroes',
      }),
    ],
  ]);
}

export function getInitializeGroupMemberPointerInstructionDataCodec(): FixedSizeCodec<
  InitializeGroupMemberPointerInstructionDataArgs,
  InitializeGroupMemberPointerInstructionData
> {
  return combineCodec(
    getInitializeGroupMemberPointerInstructionDataEncoder(),
    getInitializeGroupMemberPointerInstructionDataDecoder()
  );
}

export type InitializeGroupMemberPointerInput<
  TAccountMint extends string = string,
> = {
  /** The mint to initialize. */
  mint: Address<TAccountMint>;
  authority: InitializeGroupMemberPointerInstructionDataArgs['authority'];
  memberAddress: InitializeGroupMemberPointerInstructionDataArgs['memberAddress'];
};

export function getInitializeGroupMemberPointerInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeGroupMemberPointerInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): InitializeGroupMemberPointerInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeGroupMemberPointerInstructionDataEncoder().encode(
      args as InitializeGroupMemberPointerInstructionDataArgs
    ),
  } as InitializeGroupMemberPointerInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedInitializeGroupMemberPointerInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint to initialize. */
    mint: TAccountMetas[0];
  };
  data: InitializeGroupMemberPointerInstructionData;
};

export function parseInitializeGroupMemberPointerInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeGroupMemberPointerInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getInitializeGroupMemberPointerInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { findAssociatedTokenPda } from '../pdas';
import { ASSOCIATED_TOKEN_PROGRAM_ADDRESS } from '../programs';
import {
  expectAddress,
  getAccountMetaFactory,
  type ResolvedAccount,
} from '../shared';

export const CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR = 0;

export function getCreateAssociatedTokenDiscriminatorBytes() {
  return getU8Encoder().encode(CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR);
}

export type CreateAssociatedTokenInstruction<
  TProgram extends string = typeof ASSOCIATED_TOKEN_PROGRAM_ADDRESS,
  TAccountPayer extends string | AccountMeta<string> = string,
  TAccountAta extends string | AccountMeta<string> = string,
  TAccountOwner extends string | AccountMeta<string> = string,
  TAccountMint extends string | AccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | AccountMeta<string> = '11111111111111111111111111111111',
  TAccountTokenProgram extends
    | string
    | AccountMeta<string> = 'TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb',
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountPayer extends string
        ? WritableSignerAccount<TAccountPayer> &
            AccountSignerMeta<TAccountPayer>
        : TAccountPayer,
      TAccountAta extends string ? WritableAccount<TAccountAta> : TAccountAta,
      TAccountOwner extends string
        ? ReadonlyAccount<TAccountOwner>
        : TAccountOwner,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      TAccountTokenProgram extends string
        ? ReadonlyAccount<TAccountTokenProgram>
        : TAccountTokenProgram,
      ...TRemainingAccounts,
    ]
  >;

export type CreateAssociatedTokenInstructionData = { discriminator: number };

export type CreateAssociatedTokenInstructionDataArgs = {};

export function getCreateAssociatedTokenInstructionDataEncoder(): FixedSizeEncoder<CreateAssociatedTokenInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([['discriminator', getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR,
    })
  );
}

export function getCreateAssociatedTokenInstructionDataDecoder(): FixedSizeDecoder<CreateAssociatedTokenInstructionData> {
  return getStructDecoder([['discriminator', getU8Decoder()]]);
}

export function getCreateAssociatedTokenInstructionDataCodec(): FixedSizeCodec<
  CreateAssociatedTokenInstructionDataArgs,
  CreateAssociatedTokenInstructionData
> {
  return combineCodec(
    getCreateAssociatedTokenInstructionDataEncoder(),
    getCreateAssociatedTokenInstructionDataDecoder()
  );
}

export type CreateAssociatedTokenAsyncInput<
  TAccountPayer extends string = string,
  TAccountAta extends string = string,
  TAccountOwner extends string = string,
  TAccountMint extends string = string,
  TAccountSystemProgram extends string = string,
  TAccountTokenProgram extends string = string,
> = {
  /** Funding account (must be a system account). */
  payer: TransactionSigner<TAccountPayer>;
  /** Associated token account address to be created. */
  ata?: Address<TAccountAta>;
  /** Wallet address for the new associated token account. */
  owner: Address<TAccountOwner>;
  /** The token mint for the new associated token account. */
  mint: Address<TAccountMint>;
  /** System program. */
  systemProgram?: Address<TAccountSystemProgram>;
  /** SPL Token program. */
  tokenProgram?: Address<TAccountTokenProgram>;
};

export async function getCreateAssociatedTokenInstructionAsync<
  TAccountPayer extends string,
  TAccountAta extends string,
  TAccountOwner extends string,
  TAccountMint extends string,
  TAccountSystemProgram extends string,
  TAccountTokenProgram extends string,
  TProgramAddress extends Address = typeof ASSOCIATED_TOKEN_PROGRAM_ADDRESS,
>(
  input: CreateAssociatedTokenAsyncInput<
    TAccountPayer,
    TAccountAta,
    TAccountOwner,
    TAccountMint,
    TAccountSystemProgram,
    TAccountTokenProgram
  >,
  config?: { programAddress?: TProgramAddress }
): Promise<
  CreateAssociatedTokenInstruction<
    TProgramAddress,
    TAccountPayer,
    TAccountAta,
    TAccountOwner,
    TAccountMint,
    TAccountSystemProgram,
    TAccountTokenProgram
  >
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    payer: { value: input.payer ?? null, isWritable: true },
    ata: { value: input.ata ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Resolve default values.
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value =
      'TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb' as Address<'TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb'>;
  }
  if (!accounts.ata.value) {
    accounts.ata.value = await findAssociatedTokenPda({
      owner: expectAddress(accounts.owner.value),
      tokenProgram: expectAddress(accounts.tokenProgram.value),
      mint: expectAddress(accounts.mint.value),
    });
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.ata),
      getAccountMeta(accounts.owner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.systemProgram),
      getAccountMeta(accounts.tokenProgram),
    ],
    programAddress,
    data: getCreateAssociatedTokenInstructionDataEncoder().encode({}),
  } as CreateAssociatedTokenInstruction<
    TProgramAddress,
    TAccountPayer,
    TAccountAta,
    TAccountOwner,
    TAccountMint,
    TAccountSystemProgram,
    TAccountTokenProgram
  >;

  return instruction;
}

export type CreateAssociatedTokenInput<
  TAccountPayer extends string = string,
  TAccountAta extends string = string,
  TAccountOwner extends string = string,
  TAccountMint extends string = string,
  TAccountSystemProgram extends string = string,
  TAccountTokenProgram extends string = string,
> = {
  /** Funding account (must be a system account). */
  payer: TransactionSigner<TAccountPayer>;
  /** Associated token account address to be created. */
  ata: Address<TAccountAta>;
  /** Wallet address for the new associated token account. */
  owner: Address<TAccountOwner>;
  /** The token mint for the new associated token account. */
  mint: Address<TAccountMint>;
  /** System program. */
  systemProgram?: Address<TAccountSystemProgram>;
  /** SPL Token program. */
  tokenProgram?: Address<TAccountTokenProgram>;
};

export function getCreateAssociatedTokenInstruction<
  TAccountPayer extends string,
  TAccountAta extends string,
  TAccountOwner extends string,
  TAccountMint extends string,
  TAccountSystemProgram extends string,
  TAccountTokenProgram extends string,
  TProgramAddress extends Address = typeof ASSOCIATED_TOKEN_PROGRAM_ADDRESS,
>(
  input: CreateAssociatedTokenInput<
    TAccountPayer,
    TAccountAta,
    TAccountOwner,
    TAccountMint,
    TAccountSystemProgram,
    TAccountTokenProgram
  >,
  config?: { programAddress?: TProgramAddress }
): CreateAssociatedTokenInstruction<
  TProgramAddress,
  TAccountPayer,
  TAccountAta,
  TAccountOwner,
  TAccountMint,
  TAccountSystemProgram,
  TAccountTokenProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    payer: { value: input.payer ?? null, isWritable: true },
    ata: { value: input.ata ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Resolve default values.
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value =
      'TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb' as Address<'TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb'>;
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.ata),
      getAccountMeta(accounts.owner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.systemProgram),
      getAccountMeta(accounts.tokenProgram),
    ],
    programAddress,
    data: getCreateAssociatedTokenInstructionDataEncoder().encode({}),
  } as CreateAssociatedTokenInstruction<
    TProgramAddress,
    TAccountPayer,
    TAccountAta,
    TAccountOwner,
    TAccountMint,
    TAccountSystemProgram,
    TAccountTokenProgram
  >;

  return instruction;
}

export type ParsedCreateAssociatedTokenInstruction<
  TProgram extends string = typeof ASSOCIATED_TOKEN_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** Funding account (must be a system account). */
    payer: TAccountMetas[0];
    /** Associated token account address to be created. */
    ata: TAccountMetas[1];
    /** Wallet address for the new associated token account. */
    owner: TAccountMetas[2];
    /** The token mint for the new associated token account. */
    mint: TAccountMetas[3];
    /** System program. */
    systemProgram: TAccountMetas[4];
    /** SPL Token program. */
    tokenProgram: TAccountMetas[5];
  };
  data: CreateAssociatedTokenInstructionData;
};

export function parseCreateAssociatedTokenInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedCreateAssociatedTokenInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 6) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      payer: getNextAccount(),
      ata: getNextAccount(),
      owner: getNextAccount(),
      mint: getNextAccount(),
      systemProgram: getNextAccount(),
      tokenProgram: getNextAccount(),
    },
    data: getCreateAssociatedTokenInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR = 21;

export function getGetAccountDataSizeDiscriminatorBytes() {
  return getU8Encoder().encode(GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR);
}

export type GetAccountDataSizeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type GetAccountDataSizeInstructionData = { discriminator: number };

export type GetAccountDataSizeInstructionDataArgs = {};

export function getGetAccountDataSizeInstructionDataEncoder(): FixedSizeEncoder<GetAccountDataSizeInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([['discriminator', getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR,
    })
  );
}

export function getGetAccountDataSizeInstructionDataDecoder(): FixedSizeDecoder<GetAccountDataSizeInstructionData> {
  return getStructDecoder([['discriminator', getU8Decoder()]]);
}

export function getGetAccountDataSizeInstructionDataCodec(): FixedSizeCodec<
  GetAccountDataSizeInstructionDataArgs,
  GetAccountDataSizeInstructionData
> {
  return combineCodec(
    getGetAccountDataSizeInstructionDataEncoder(),
    getGetAccountDataSizeInstructionDataDecoder()
  );
}

export type GetAccountDataSizeInput<TAccountMint extends string = string> = {
  /** The mint to calculate for. */
  mint: Address<TAccountMint>;
};

export function getGetAccountDataSizeInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: GetAccountDataSizeInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): GetAccountDataSizeInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getGetAccountDataSizeInstructionDataEncoder().encode({}),
  } as GetAccountDataSizeInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedGetAccountDataSizeInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint to calculate for. */
    mint: TAccountMetas[0];
  };
  data: GetAccountDataSizeInstructionData;
};

export function parseGetAccountDataSizeInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedGetAccountDataSizeInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getGetAccountDataSizeInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getBytesDecoder,
  getBytesEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  none,
  transformEncoder,
  type AccountMeta,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const EMIT_TOKEN_METADATA_DISCRIMINATOR = new Uint8Array([
  250, 166, 180, 250, 13, 12, 184, 70,
]);

export function getEmitTokenMetadataDiscriminatorBytes() {
  return getBytesEncoder().encode(EMIT_TOKEN_METADATA_DISCRIMINATOR);
}

export type EmitTokenMetadataInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetadata extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMetadata extends string
        ? ReadonlyAccount<TAccountMetadata>
        : TAccountMetadata,
      ...TRemainingAccounts,
    ]
  >;

export type EmitTokenMetadataInstructionData = {
  discriminator: ReadonlyUint8Array;
  /** Start of range of data to emit */
  start: Option<bigint>;
  /** End of range of data to emit */
  end: Option<bigint>;
};

export type EmitTokenMetadataInstructionDataArgs = {
  /** Start of range of data to emit */
  start?: OptionOrNullable<number | bigint>;
  /** End of range of data to emit */
  end?: OptionOrNullable<number | bigint>;
};

export function getEmitTokenMetadataInstructionDataEncoder(): Encoder<EmitTokenMetadataInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getBytesEncoder()],
      ['start', getOptionEncoder(getU64Encoder())],
      ['end', getOptionEncoder(getU64Encoder())],
    ]),
    (value) => ({
      ...value,
      discriminator: EMIT_TOKEN_METADATA_DISCRIMINATOR,
      start: value.start ?? none(),
      end: value.end ?? none(),
    })
  );
}

export function getEmitTokenMetadataInstructionDataDecoder(): Decoder<EmitTokenMetadataInstructionData> {
  return getStructDecoder([
    ['discriminator', getBytesDecoder()],
    ['start', getOptionDecoder(getU64Decoder())],
    ['end', getOptionDecoder(getU64Decoder())],
  ]);
}

export function getEmitTokenMetadataInstructionDataCodec(): Codec<
  EmitTokenMetadataInstructionDataArgs,
  EmitTokenMetadataInstructionData
> {
  return combineCodec(
    getEmitTokenMetadataInstructionDataEncoder(),
    getEmitTokenMetadataInstructionDataDecoder()
  );
}

export type EmitTokenMetadataInput<TAccountMetadata extends string = string> = {
  metadata: Address<TAccountMetadata>;
  start?: EmitTokenMetadataInstructionDataArgs['start'];
  end?: EmitTokenMetadataInstructionDataArgs['end'];
};

export function getEmitTokenMetadataInstruction<
  TAccountMetadata extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: EmitTokenMetadataInput<TAccountMetadata>,
  config?: { programAddress?: TProgramAddress }
): EmitTokenMetadataInstruction<TProgramAddress, TAccountMetadata> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.metadata)],
    programAddress,
    data: getEmitTokenMetadataInstructionDataEncoder().encode(
      args as EmitTokenMetadataInstructionDataArgs
    ),
  } as EmitTokenMetadataInstruction<TProgramAddress, TAccountMetadata>;

  return instruction;
}

export type ParsedEmitTokenMetadataInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    metadata: TAccountMetas[0];
  };
  data: EmitTokenMetadataInstructionData;
};

export function parseEmitTokenMetadataInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedEmitTokenMetadataInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount(),
    },
    data: getEmitTokenMetadataInstructionDataDecoder().decode(instruction.data),
  };
}

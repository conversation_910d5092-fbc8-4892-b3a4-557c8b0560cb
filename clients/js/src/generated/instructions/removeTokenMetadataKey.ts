/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  addDecoderSizePrefix,
  addEncoderSizePrefix,
  combineCodec,
  getBooleanDecoder,
  getBooleanEncoder,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU32Decoder,
  getU32Encoder,
  getUtf8Decoder,
  getUtf8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR = new Uint8Array([
  234, 18, 32, 56, 89, 141, 37, 181,
]);

export function getRemoveTokenMetadataKeyDiscriminatorBytes() {
  return getBytesEncoder().encode(REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR);
}

export type RemoveTokenMetadataKeyInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetadata extends string | AccountMeta<string> = string,
  TAccountUpdateAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMetadata extends string
        ? WritableAccount<TAccountMetadata>
        : TAccountMetadata,
      TAccountUpdateAuthority extends string
        ? ReadonlySignerAccount<TAccountUpdateAuthority> &
            AccountSignerMeta<TAccountUpdateAuthority>
        : TAccountUpdateAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type RemoveTokenMetadataKeyInstructionData = {
  discriminator: ReadonlyUint8Array;
  /**
   * If the idempotent flag is set to true, then the instruction will not
   * error if the key does not exist
   */
  idempotent: boolean;
  /** Key to remove in the additional metadata portion. */
  key: string;
};

export type RemoveTokenMetadataKeyInstructionDataArgs = {
  /**
   * If the idempotent flag is set to true, then the instruction will not
   * error if the key does not exist
   */
  idempotent?: boolean;
  /** Key to remove in the additional metadata portion. */
  key: string;
};

export function getRemoveTokenMetadataKeyInstructionDataEncoder(): Encoder<RemoveTokenMetadataKeyInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getBytesEncoder()],
      ['idempotent', getBooleanEncoder()],
      ['key', addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder())],
    ]),
    (value) => ({
      ...value,
      discriminator: REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR,
      idempotent: value.idempotent ?? false,
    })
  );
}

export function getRemoveTokenMetadataKeyInstructionDataDecoder(): Decoder<RemoveTokenMetadataKeyInstructionData> {
  return getStructDecoder([
    ['discriminator', getBytesDecoder()],
    ['idempotent', getBooleanDecoder()],
    ['key', addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())],
  ]);
}

export function getRemoveTokenMetadataKeyInstructionDataCodec(): Codec<
  RemoveTokenMetadataKeyInstructionDataArgs,
  RemoveTokenMetadataKeyInstructionData
> {
  return combineCodec(
    getRemoveTokenMetadataKeyInstructionDataEncoder(),
    getRemoveTokenMetadataKeyInstructionDataDecoder()
  );
}

export type RemoveTokenMetadataKeyInput<
  TAccountMetadata extends string = string,
  TAccountUpdateAuthority extends string = string,
> = {
  metadata: Address<TAccountMetadata>;
  updateAuthority: TransactionSigner<TAccountUpdateAuthority>;
  idempotent?: RemoveTokenMetadataKeyInstructionDataArgs['idempotent'];
  key: RemoveTokenMetadataKeyInstructionDataArgs['key'];
};

export function getRemoveTokenMetadataKeyInstruction<
  TAccountMetadata extends string,
  TAccountUpdateAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: RemoveTokenMetadataKeyInput<TAccountMetadata, TAccountUpdateAuthority>,
  config?: { programAddress?: TProgramAddress }
): RemoveTokenMetadataKeyInstruction<
  TProgramAddress,
  TAccountMetadata,
  TAccountUpdateAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false,
    },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.metadata),
      getAccountMeta(accounts.updateAuthority),
    ],
    programAddress,
    data: getRemoveTokenMetadataKeyInstructionDataEncoder().encode(
      args as RemoveTokenMetadataKeyInstructionDataArgs
    ),
  } as RemoveTokenMetadataKeyInstruction<
    TProgramAddress,
    TAccountMetadata,
    TAccountUpdateAuthority
  >;

  return instruction;
}

export type ParsedRemoveTokenMetadataKeyInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    metadata: TAccountMetas[0];
    updateAuthority: TAccountMetas[1];
  };
  data: RemoveTokenMetadataKeyInstructionData;
};

export function parseRemoveTokenMetadataKeyInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedRemoveTokenMetadataKeyInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount(),
      updateAuthority: getNextAccount(),
    },
    data: getRemoveTokenMetadataKeyInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

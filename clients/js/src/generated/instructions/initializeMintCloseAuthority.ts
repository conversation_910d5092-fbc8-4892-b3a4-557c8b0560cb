/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  getAddressDecoder,
  getAddressEncoder,
  getOptionDecoder,
  getOptionEncoder,
  getStructDecoder,
  getStructEncoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type Option,
  type OptionOrNullable,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR = 25;

export function getInitializeMintCloseAuthorityDiscriminatorBytes() {
  return getU8Encoder().encode(INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR);
}

export type InitializeMintCloseAuthorityInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMint extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountMint extends string
        ? WritableAccount<TAccountMint>
        : TAccountMint,
      ...TRemainingAccounts,
    ]
  >;

export type InitializeMintCloseAuthorityInstructionData = {
  discriminator: number;
  /** Authority that must sign the `CloseAccount` instruction on a mint. */
  closeAuthority: Option<Address>;
};

export type InitializeMintCloseAuthorityInstructionDataArgs = {
  /** Authority that must sign the `CloseAccount` instruction on a mint. */
  closeAuthority: OptionOrNullable<Address>;
};

export function getInitializeMintCloseAuthorityInstructionDataEncoder(): Encoder<InitializeMintCloseAuthorityInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['closeAuthority', getOptionEncoder(getAddressEncoder())],
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR,
    })
  );
}

export function getInitializeMintCloseAuthorityInstructionDataDecoder(): Decoder<InitializeMintCloseAuthorityInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['closeAuthority', getOptionDecoder(getAddressDecoder())],
  ]);
}

export function getInitializeMintCloseAuthorityInstructionDataCodec(): Codec<
  InitializeMintCloseAuthorityInstructionDataArgs,
  InitializeMintCloseAuthorityInstructionData
> {
  return combineCodec(
    getInitializeMintCloseAuthorityInstructionDataEncoder(),
    getInitializeMintCloseAuthorityInstructionDataDecoder()
  );
}

export type InitializeMintCloseAuthorityInput<
  TAccountMint extends string = string,
> = {
  /** The mint to initialize. */
  mint: Address<TAccountMint>;
  closeAuthority: InitializeMintCloseAuthorityInstructionDataArgs['closeAuthority'];
};

export function getInitializeMintCloseAuthorityInstruction<
  TAccountMint extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: InitializeMintCloseAuthorityInput<TAccountMint>,
  config?: { programAddress?: TProgramAddress }
): InitializeMintCloseAuthorityInstruction<TProgramAddress, TAccountMint> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeMintCloseAuthorityInstructionDataEncoder().encode(
      args as InitializeMintCloseAuthorityInstructionDataArgs
    ),
  } as InitializeMintCloseAuthorityInstruction<TProgramAddress, TAccountMint>;

  return instruction;
}

export type ParsedInitializeMintCloseAuthorityInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The mint to initialize. */
    mint: TAccountMetas[0];
  };
  data: InitializeMintCloseAuthorityInstructionData;
};

export function parseInitializeMintCloseAuthorityInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedInitializeMintCloseAuthorityInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 1) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
    },
    data: getInitializeMintCloseAuthorityInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  AccountRole,
  combineCodec,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type AccountMeta,
  type AccountSignerMeta,
  type Address,
  type FixedSizeCodec,
  type FixedSizeDecoder,
  type FixedSizeEncoder,
  type Instruction,
  type InstructionWithAccounts,
  type InstructionWithData,
  type ReadonlyAccount,
  type ReadonlySignerAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
} from '@solana/kit';
import { TOKEN_2022_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';
import {
  getDecryptableBalanceDecoder,
  getD<PERSON>ryptableBalanceEncoder,
  type DecryptableBalance,
  type DecryptableBalanceArgs,
} from '../types';

export const APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR = 27;

export function getApplyConfidentialPendingBalanceDiscriminatorBytes() {
  return getU8Encoder().encode(
    APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR
  );
}

export const APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 8;

export function getApplyConfidentialPendingBalanceConfidentialTransferDiscriminatorBytes() {
  return getU8Encoder().encode(
    APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}

export type ApplyConfidentialPendingBalanceInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountToken extends string | AccountMeta<string> = string,
  TAccountAuthority extends string | AccountMeta<string> = string,
  TRemainingAccounts extends readonly AccountMeta<string>[] = [],
> = Instruction<TProgram> &
  InstructionWithData<ReadonlyUint8Array> &
  InstructionWithAccounts<
    [
      TAccountToken extends string
        ? WritableAccount<TAccountToken>
        : TAccountToken,
      TAccountAuthority extends string
        ? ReadonlyAccount<TAccountAuthority>
        : TAccountAuthority,
      ...TRemainingAccounts,
    ]
  >;

export type ApplyConfidentialPendingBalanceInstructionData = {
  discriminator: number;
  confidentialTransferDiscriminator: number;
  /**
   * The expected number of pending balance credits since the last successful
   * `ApplyPendingBalance` instruction
   */
  expectedPendingBalanceCreditCounter: bigint;
  /**
   * The new decryptable balance if the pending balance is applied
   * successfully
   */
  newDecryptableAvailableBalance: DecryptableBalance;
};

export type ApplyConfidentialPendingBalanceInstructionDataArgs = {
  /**
   * The expected number of pending balance credits since the last successful
   * `ApplyPendingBalance` instruction
   */
  expectedPendingBalanceCreditCounter: number | bigint;
  /**
   * The new decryptable balance if the pending balance is applied
   * successfully
   */
  newDecryptableAvailableBalance: DecryptableBalanceArgs;
};

export function getApplyConfidentialPendingBalanceInstructionDataEncoder(): FixedSizeEncoder<ApplyConfidentialPendingBalanceInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', getU8Encoder()],
      ['confidentialTransferDiscriminator', getU8Encoder()],
      ['expectedPendingBalanceCreditCounter', getU64Encoder()],
      ['newDecryptableAvailableBalance', getDecryptableBalanceEncoder()],
    ]),
    (value) => ({
      ...value,
      discriminator: APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR,
      confidentialTransferDiscriminator:
        APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR,
    })
  );
}

export function getApplyConfidentialPendingBalanceInstructionDataDecoder(): FixedSizeDecoder<ApplyConfidentialPendingBalanceInstructionData> {
  return getStructDecoder([
    ['discriminator', getU8Decoder()],
    ['confidentialTransferDiscriminator', getU8Decoder()],
    ['expectedPendingBalanceCreditCounter', getU64Decoder()],
    ['newDecryptableAvailableBalance', getDecryptableBalanceDecoder()],
  ]);
}

export function getApplyConfidentialPendingBalanceInstructionDataCodec(): FixedSizeCodec<
  ApplyConfidentialPendingBalanceInstructionDataArgs,
  ApplyConfidentialPendingBalanceInstructionData
> {
  return combineCodec(
    getApplyConfidentialPendingBalanceInstructionDataEncoder(),
    getApplyConfidentialPendingBalanceInstructionDataDecoder()
  );
}

export type ApplyConfidentialPendingBalanceInput<
  TAccountToken extends string = string,
  TAccountAuthority extends string = string,
> = {
  /** The SPL Token account. */
  token: Address<TAccountToken>;
  /** The source account's owner/delegate or its multisignature account. */
  authority: Address<TAccountAuthority> | TransactionSigner<TAccountAuthority>;
  expectedPendingBalanceCreditCounter: ApplyConfidentialPendingBalanceInstructionDataArgs['expectedPendingBalanceCreditCounter'];
  newDecryptableAvailableBalance: ApplyConfidentialPendingBalanceInstructionDataArgs['newDecryptableAvailableBalance'];
  multiSigners?: Array<TransactionSigner>;
};

export function getApplyConfidentialPendingBalanceInstruction<
  TAccountToken extends string,
  TAccountAuthority extends string,
  TProgramAddress extends Address = typeof TOKEN_2022_PROGRAM_ADDRESS,
>(
  input: ApplyConfidentialPendingBalanceInput<TAccountToken, TAccountAuthority>,
  config?: { programAddress?: TProgramAddress }
): ApplyConfidentialPendingBalanceInstruction<
  TProgramAddress,
  TAccountToken,
  (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
    ? ReadonlySignerAccount<TAccountAuthority> &
        AccountSignerMeta<TAccountAuthority>
    : TAccountAuthority
> {
  // Program address.
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Remaining accounts.
  const remainingAccounts: AccountMeta[] = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: AccountRole.READONLY_SIGNER,
      signer,
    })
  );

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.authority),
      ...remainingAccounts,
    ],
    programAddress,
    data: getApplyConfidentialPendingBalanceInstructionDataEncoder().encode(
      args as ApplyConfidentialPendingBalanceInstructionDataArgs
    ),
  } as ApplyConfidentialPendingBalanceInstruction<
    TProgramAddress,
    TAccountToken,
    (typeof input)['authority'] extends TransactionSigner<TAccountAuthority>
      ? ReadonlySignerAccount<TAccountAuthority> &
          AccountSignerMeta<TAccountAuthority>
      : TAccountAuthority
  >;

  return instruction;
}

export type ParsedApplyConfidentialPendingBalanceInstruction<
  TProgram extends string = typeof TOKEN_2022_PROGRAM_ADDRESS,
  TAccountMetas extends readonly AccountMeta[] = readonly AccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    /** The SPL Token account. */
    token: TAccountMetas[0];
    /** The source account's owner/delegate or its multisignature account. */
    authority: TAccountMetas[1];
  };
  data: ApplyConfidentialPendingBalanceInstructionData;
};

export function parseApplyConfidentialPendingBalanceInstruction<
  TProgram extends string,
  TAccountMetas extends readonly AccountMeta[],
>(
  instruction: Instruction<TProgram> &
    InstructionWithAccounts<TAccountMetas> &
    InstructionWithData<ReadonlyUint8Array>
): ParsedApplyConfidentialPendingBalanceInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 2) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = (instruction.accounts as TAccountMetas)[accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      authority: getNextAccount(),
    },
    data: getApplyConfidentialPendingBalanceInstructionDataDecoder().decode(
      instruction.data
    ),
  };
}

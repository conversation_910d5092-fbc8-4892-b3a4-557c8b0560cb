/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  isProgramError,
  type Address,
  type SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM,
  type SolanaError,
} from '@solana/kit';
import { ASSOCIATED_TOKEN_PROGRAM_ADDRESS } from '../programs';

/** InvalidOwner: Associated token account owner does not match address derivation */
export const ASSOCIATED_TOKEN_ERROR__INVALID_OWNER = 0x0; // 0

export type AssociatedTokenError = typeof ASSOCIATED_TOKEN_ERROR__INVALID_OWNER;

let associatedTokenErrorMessages:
  | Record<AssociatedTokenError, string>
  | undefined;
if (process.env.NODE_ENV !== 'production') {
  associatedTokenErrorMessages = {
    [ASSOCIATED_TOKEN_ERROR__INVALID_OWNER]: `Associated token account owner does not match address derivation`,
  };
}

export function getAssociatedTokenErrorMessage(
  code: AssociatedTokenError
): string {
  if (process.env.NODE_ENV !== 'production') {
    return (
      associatedTokenErrorMessages as Record<AssociatedTokenError, string>
    )[code];
  }

  return 'Error message not available in production bundles.';
}

export function isAssociatedTokenError<
  TProgramErrorCode extends AssociatedTokenError,
>(
  error: unknown,
  transactionMessage: {
    instructions: Record<number, { programAddress: Address }>;
  },
  code?: TProgramErrorCode
): error is SolanaError<typeof SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM> &
  Readonly<{ context: Readonly<{ code: TProgramErrorCode }> }> {
  return isProgramError<TProgramErrorCode>(
    error,
    transactionMessage,
    ASSOCIATED_TOKEN_PROGRAM_ADDRESS,
    code
  );
}

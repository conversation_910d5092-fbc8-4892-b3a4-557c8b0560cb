{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "module": "ESNext", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "target": "ESNext"}, "exclude": ["node_modules"], "include": ["src", "test"]}
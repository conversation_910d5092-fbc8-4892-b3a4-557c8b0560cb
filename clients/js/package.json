{"name": "@solana-program/token-2022", "version": "0.5.0", "description": "JavaScript client for the Token 2022 program", "sideEffects": false, "module": "./dist/src/index.mjs", "main": "./dist/src/index.js", "types": "./dist/types/index.d.ts", "type": "commonjs", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/src/index.mjs", "require": "./dist/src/index.js"}}, "files": ["./dist/src", "./dist/types"], "scripts": {"build": "rimraf dist && tsup && tsc -p ./tsconfig.declarations.json", "build:docs": "typedoc", "test": "ava", "lint": "eslint --ext js,ts,tsx src", "lint:fix": "eslint --fix --ext js,ts,tsx src", "format": "prettier --check src test", "format:fix": "prettier --write src test", "prepublishOnly": "pnpm build"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/solana-program/token-2022.git"}, "bugs": {"url": "https://github.com/solana-program/token-2022/issues"}, "homepage": "https://github.com/solana-program/token-2022#readme", "peerDependencies": {"@solana/kit": "^3.0", "@solana/sysvars": "^3.0"}, "devDependencies": {"@ava/typescript": "^6.0.0", "@solana-program/system": "^0.8.0", "@solana/eslint-config-solana": "^3.0.3", "@solana/kit": "^3.0", "@types/node": "^24", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "ava": "^6.1.3", "eslint": "^8.57.0", "prettier": "^3.3.3", "rimraf": "^6.0.1", "tsup": "^8.1.2", "typedoc": "^0.28.0", "typescript": "^5.5.3"}, "ava": {"nodeArguments": ["--no-warnings"], "typescript": {"compile": false, "rewritePaths": {"test/": "dist/test/"}}}, "packageManager": "pnpm@9.1.0"}
[package]
name = "spl-token-2022-client"
version = "0.0.0"
description = "A generated Rust library for the Token 2022 program"
repository = "https://github.com/solana-program/token-2022"
edition = "2021"
readme = "README.md"
license-file = "../../LICENSE"

[features]
test-sbf = []
serde = ["dep:serde", "dep:serde_with"]

[dependencies]
borsh = "^0.10"
num-derive = "^0.3"
num-traits = "^0.2"
serde = { version = "^1.0", features = ["derive"], optional = true }
serde_with = { version = "^3.0", optional = true }
solana-program = "2.2.1"
thiserror = "^1.0"

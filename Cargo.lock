# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 4

[[package]]
name = "Inflector"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe438c63458706e03479442743baae6c88256498e6431708f6dfc520a26515d3"
dependencies = [
 "lazy_static",
 "regex",
]

[[package]]
name = "addr2line"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfbe277e56a376000877090da837660b4427aad530e3028d44e0bffe4f89a1c1"
dependencies = [
 "gimli",
]

[[package]]
name = "adler2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "512761e0bb2578dd7380c6baaa0f4ce03e84f95e960231d1dec8bf4d7d6e2627"

[[package]]
name = "aead"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d122413f284cf2d62fb1b7db97e02edb8cda96d769b16e443a4f6195e35662b0"
dependencies = [
 "crypto-common",
 "generic-array",
]

[[package]]
name = "aes"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b169f7a6d4742236a0a00c541b845991d0ac43e546831af1249753ab4c3aa3a0"
dependencies = [
 "cfg-if 1.0.0",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "aes-gcm-siv"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae0784134ba9375416d469ec31e7c5f9fa94405049cf08c5ce5b4698be673e0d"
dependencies = [
 "aead",
 "aes",
 "cipher",
 "ctr",
 "polyval",
 "subtle",
 "zeroize",
]

[[package]]
name = "agave-banking-stage-ingress-types"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5a44744c491f82d5dd11d71e6c07180d242bd265b423bd0ce6097b7a356c19b"
dependencies = [
 "crossbeam-channel",
 "solana-perf",
]

[[package]]
name = "agave-feature-set"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2733340e0429d146d4b77d265ae80b22e253507b30a2257ff68eccb78eab210b"
dependencies = [
 "ahash 0.8.11",
 "solana-epoch-schedule",
 "solana-hash 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sha256-hasher 2.2.1",
 "solana-svm-feature-set",
]

[[package]]
name = "agave-geyser-plugin-interface"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d6fdfd3a221921780d57ed69e2959dc2d2d9ae342815ac663870f336e25eee4"
dependencies = [
 "log",
 "solana-clock",
 "solana-signature 2.3.0",
 "solana-transaction",
 "solana-transaction-status",
 "thiserror 2.0.12",
]

[[package]]
name = "agave-io-uring"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a65c957d4688df6415a054b8c3940dd75307e770a47c840ad6cfc7e82fa98054"
dependencies = [
 "io-uring",
 "libc",
 "log",
 "slab",
 "smallvec",
]

[[package]]
name = "agave-precompiles"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba42f630a219a103926b63472fa8cef512cb578ad3be7975250af639c1bce2a7"
dependencies = [
 "agave-feature-set",
 "bincode",
 "digest 0.10.7",
 "ed25519-dalek",
 "libsecp256k1",
 "openssl",
 "sha3",
 "solana-ed25519-program",
 "solana-message",
 "solana-precompile-error",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-secp256k1-program",
 "solana-secp256r1-program",
]

[[package]]
name = "agave-reserved-account-keys"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "732a49e540c5b7b8d8943d50ad4b51b98ad9951494053b51fb909c140d3df8b1"
dependencies = [
 "agave-feature-set",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "agave-transaction-view"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e79356209e3126f9a60af1b50690be8334336b4b9e52e9ccc87e775519d78f78"
dependencies = [
 "solana-hash 2.3.0",
 "solana-message",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-short-vec",
 "solana-signature 2.3.0",
 "solana-svm-transaction",
]

[[package]]
name = "agave-xdp"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08f0f6fb0b58d7cf96dff307abfee7f00cc777016712edfba0f3f77d396f8092"
dependencies = [
 "aya",
 "caps",
 "crossbeam-channel",
 "libc",
 "log",
 "thiserror 2.0.12",
]

[[package]]
name = "ahash"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "891477e0c6a8957309ee5c45a6368af3ae14bb510732d2684ffa19af310920f9"
dependencies = [
 "getrandom 0.2.15",
 "once_cell",
 "version_check",
]

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if 1.0.0",
 "getrandom 0.2.15",
 "once_cell",
 "version_check",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "alloc-no-stdlib"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc7bb162ec39d46ab1ca8c77bf72e890535becd1751bb45f64c597edb4c8c6b3"

[[package]]
name = "alloc-stdlib"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94fb8275041c72129eb51b7d0322c29b8387a0386127718b096429201a5d6ece"
dependencies = [
 "alloc-no-stdlib",
]

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "ansi_term"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52a9bb7ec0cf484c551830a7ce27bd20d67eac647e1befb56b0be4ee39a55d2"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "anstream"
version = "0.6.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8acc5369981196006228e28809f761875c0327210a891e941f4c683b3a99529b"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "is_terminal_polyfill",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55cc3b69f167a1ef2e161439aa98aed94e6028e5f9a59be9a6ffb47aef1651f9"

[[package]]
name = "anstyle-parse"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b2d16507662817a6a20a9ea92df6652ee4f94f914589377d69f3b21bc5798a9"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79947af37f4177cfead1110013d678905c37501914fba0efea834c3fe9a8d60c"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3534e77181a9cc07539ad51f2141fe32f6c3ffd4df76db8ad92346b003ae4e"
dependencies = [
 "anstyle",
 "once_cell",
 "windows-sys 0.59.0",
]

[[package]]
name = "anyhow"
version = "1.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16d2d3311acee920a9eb8d33b8cbc1787ce4a264e85f964c2404b969bdcd487"

[[package]]
name = "aquamarine"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f50776554130342de4836ba542aa85a4ddb361690d7e8df13774d7284c3d5c2"
dependencies = [
 "include_dir",
 "itertools 0.10.5",
 "proc-macro-error2",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "arc-swap"
version = "1.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69f7f8c3906b62b754cd5326047894316021dcfe5a194c8ea52bdd94934a3457"

[[package]]
name = "ark-bn254"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a22f4561524cd949590d78d7d4c5df8f592430d221f7f3c9497bbafd8972120f"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-std",
]

[[package]]
name = "ark-ec"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "defd9a439d56ac24968cca0571f598a61bc8c55f71d50a89cda591cb750670ba"
dependencies = [
 "ark-ff",
 "ark-poly",
 "ark-serialize",
 "ark-std",
 "derivative",
 "hashbrown 0.13.2",
 "itertools 0.10.5",
 "num-traits",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec847af850f44ad29048935519032c33da8aa03340876d351dfab5660d2966ba"
dependencies = [
 "ark-ff-asm",
 "ark-ff-macros",
 "ark-serialize",
 "ark-std",
 "derivative",
 "digest 0.10.7",
 "itertools 0.10.5",
 "num-bigint 0.4.6",
 "num-traits",
 "paste",
 "rustc_version",
 "zeroize",
]

[[package]]
name = "ark-ff-asm"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ed4aa4fe255d0bc6d79373f7e31d2ea147bcf486cba1be5ba7ea85abdb92348"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7abe79b0e4288889c4574159ab790824d0033b9fdcb2a112a3182fac2e514565"
dependencies = [
 "num-bigint 0.4.6",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-poly"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d320bfc44ee185d899ccbadfa8bc31aab923ce1558716e1997a1e74057fe86bf"
dependencies = [
 "ark-ff",
 "ark-serialize",
 "ark-std",
 "derivative",
 "hashbrown 0.13.2",
]

[[package]]
name = "ark-serialize"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb7b85a02b83d2f22f89bd5cac66c9c89474240cb6207cb1efc16d098e822a5"
dependencies = [
 "ark-serialize-derive",
 "ark-std",
 "digest 0.10.7",
 "num-bigint 0.4.6",
]

[[package]]
name = "ark-serialize-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae3281bc6d0fd7e549af32b52511e1302185bd688fd3359fa36423346ff682ea"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-std"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94893f1e0c6eeab764ade8dc4c0db24caf4fe7cbbaafc0eba0a9030f447b5185"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "arrayref"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76a2e8124351fda1ef8aaaa3bbd7ebbcb486bbcd4225aca0aa0d84bb2db8fecb"

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"

[[package]]
name = "ascii"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eab1c04a571841102f5345a8fc0f6bb3d31c315dec879b5c6e42e40ce7ffa34e"

[[package]]
name = "asn1-rs"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f6fd5ddaf0351dff5b8da21b2fb4ff8e08ddd02857f0bf69c47639106c0fff0"
dependencies = [
 "asn1-rs-derive",
 "asn1-rs-impl",
 "displaydoc",
 "nom",
 "num-traits",
 "rusticata-macros",
 "thiserror 1.0.69",
 "time",
]

[[package]]
name = "asn1-rs-derive"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "726535892e8eae7e70657b4c8ea93d26b8553afb1ce617caee529ef96d7dee6c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "synstructure 0.12.6",
]

[[package]]
name = "asn1-rs-impl"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2777730b2039ac0f95f093556e61b6d26cebed5393ca6f152717777cec3a42ed"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "assert_cmd"
version = "2.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bd389a4b2970a01282ee455294913c0a43724daedcd1a24c3eb0ec1c1320b66"
dependencies = [
 "anstyle",
 "bstr",
 "doc-comment",
 "libc",
 "predicates 3.1.3",
 "predicates-core",
 "predicates-tree",
 "wait-timeout",
]

[[package]]
name = "assert_matches"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b34d609dfbaf33d6889b2b7106d3ca345eacad44200913df5ba02bfd31d2ba9"

[[package]]
name = "async-channel"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81953c529336010edd6d8e358f886d9581267795c61b19475b71314bffa46d35"
dependencies = [
 "concurrent-queue",
 "event-listener 2.5.3",
 "futures-core",
]

[[package]]
name = "async-compression"
version = "0.4.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "310c9bcae737a48ef5cdee3174184e6d548b292739ede61a1f955ef76a738861"
dependencies = [
 "brotli",
 "flate2",
 "futures-core",
 "memchr",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "async-lock"
version = "3.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff6e472cdea888a4bd64f342f09b3f50e1886d32afe8df3d663c01140b811b18"
dependencies = [
 "event-listener 5.4.0",
 "event-listener-strategy",
 "pin-project-lite",
]

[[package]]
name = "async-stream"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b5a71a6f37880a80d1d7f19efd781e4b5de42c88f0722cc13bcb6cc2cfe8476"
dependencies = [
 "async-stream-impl",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-stream-impl"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7c24de15d275a1ecfd47a380fb4d5ec9bfe0933f309ed5e705b775596a3574d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "async-trait"
version = "0.1.88"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e539d3fca749fcee5236ab05e93a52867dd549cc157c8cb7f99595f3cedffdb5"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "atty"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9b39be18770d11421cdb1b9947a45dd3f37e93092cbf377614828a319d5fee8"
dependencies = [
 "hermit-abi 0.1.19",
 "libc",
 "winapi 0.3.9",
]

[[package]]
name = "autocfg"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ace50bade8e6234aa140d9a2f552bbee1db4d353f69b8217bc503490fc1a9f26"

[[package]]
name = "autotools"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef941527c41b0fc0dd48511a8154cd5fc7e29200a0ff8b7203c5d777dbc795cf"
dependencies = [
 "cc",
]

[[package]]
name = "axum"
version = "0.6.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b829e4e32b91e643de6eafe82b1d90675f5874230191a4ffbc1b336dec4d6bf"
dependencies = [
 "async-trait",
 "axum-core",
 "bitflags 1.3.2",
 "bytes",
 "futures-util",
 "http 0.2.12",
 "http-body 0.4.6",
 "hyper 0.14.32",
 "itoa",
 "matchit",
 "memchr",
 "mime",
 "percent-encoding 2.3.1",
 "pin-project-lite",
 "rustversion",
 "serde",
 "sync_wrapper 0.1.2",
 "tower 0.4.13",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "759fa577a247914fd3f7f76d62972792636412fbfd634cd452f6a385a74d2d2c"
dependencies = [
 "async-trait",
 "bytes",
 "futures-util",
 "http 0.2.12",
 "http-body 0.4.6",
 "mime",
 "rustversion",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "aya"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d18bc4e506fbb85ab7392ed993a7db4d1a452c71b75a246af4a80ab8c9d2dd50"
dependencies = [
 "assert_matches",
 "aya-obj",
 "bitflags 2.9.1",
 "bytes",
 "libc",
 "log",
 "object",
 "once_cell",
 "thiserror 1.0.69",
]

[[package]]
name = "aya-obj"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c51b96c5a8ed8705b40d655273bc4212cbbf38d4e3be2788f36306f154523ec7"
dependencies = [
 "bytes",
 "core-error",
 "hashbrown 0.15.2",
 "log",
 "object",
 "thiserror 1.0.69",
]

[[package]]
name = "backoff"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b62ddb9cb1ec0a098ad4bbf9344d0713fa193ae1a80af55febcff2627b6a00c1"
dependencies = [
 "futures-core",
 "getrandom 0.2.15",
 "instant",
 "pin-project-lite",
 "rand 0.8.5",
 "tokio",
]

[[package]]
name = "backtrace"
version = "0.3.74"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d82cb332cdfaed17ae235a638438ac4d4839913cc2af585c3c6746e8f8bee1a"
dependencies = [
 "addr2line",
 "cfg-if 1.0.0",
 "libc",
 "miniz_oxide",
 "object",
 "rustc-demangle",
 "windows-targets 0.52.6",
]

[[package]]
name = "base64"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3441f0f7b02788e948e47f457ca01f1d7e6d92c693bc132c22b087d3141c03ff"

[[package]]
name = "base64"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e1b586273c5702936fe7b7d6896644d8be71e6314cfe09d3167c95f712589e8"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.69.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271383c67ccabffb7381723dea0672a673f292304fcb45c01cc648c7a8d58088"
dependencies = [
 "bitflags 2.9.1",
 "cexpr",
 "clang-sys",
 "itertools 0.12.1",
 "lazy_static",
 "lazycell",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex",
 "syn 2.0.99",
]

[[package]]
name = "bit-set"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08807e080ed7f9d5433fa9b275196cfc35414f66a0c79d864dc51a0d825231a3"
dependencies = [
 "bit-vec",
]

[[package]]
name = "bit-vec"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e764a1d40d510daf35e07be9eb06e75770908c27d411ee6c92109c9840eaaf7"

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b8e56985ec62d17e9c1001dc89c88ecd7dc08e47eba5ec7c29c7b5eeecde967"
dependencies = [
 "serde",
]

[[package]]
name = "bitmaps"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "031043d04099746d8db04daf1fa424b2bc8bd69d92b25962dcde24da39ab64a2"
dependencies = [
 "typenum",
]

[[package]]
name = "blake3"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3888aaa89e4b2a40fca9848e400f6a658a5a3978de7be858e209cafa8be9a4a0"
dependencies = [
 "arrayref",
 "arrayvec",
 "cc",
 "cfg-if 1.0.0",
 "constant_time_eq",
 "digest 0.10.7",
]

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array",
]

[[package]]
name = "borsh"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "115e54d64eb62cdebad391c19efc9dce4981c690c85a33a12199d99bb9546fee"
dependencies = [
 "borsh-derive 0.10.4",
 "hashbrown 0.13.2",
]

[[package]]
name = "borsh"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad8646f98db542e39fc66e68a20b2144f6a732636df7c2354e74645faaa433ce"
dependencies = [
 "borsh-derive 1.5.7",
 "cfg_aliases",
]

[[package]]
name = "borsh-derive"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "831213f80d9423998dd696e2c5345aba6be7a0bd8cd19e31c5243e13df1cef89"
dependencies = [
 "borsh-derive-internal",
 "borsh-schema-derive-internal",
 "proc-macro-crate 0.1.5",
 "proc-macro2",
 "syn 1.0.109",
]

[[package]]
name = "borsh-derive"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdd1d3c0c2f5833f22386f252fe8ed005c7f59fdcddeef025c01b4c3b9fd9ac3"
dependencies = [
 "once_cell",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "borsh-derive-internal"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65d6ba50644c98714aa2a70d13d7df3cd75cd2b523a2b452bf010443800976b3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "borsh-schema-derive-internal"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "276691d96f063427be83e6692b86148e488ebba9f48f77788724ca027ba3b6d4"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "brotli"
version = "7.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc97b8f16f944bba54f0433f07e30be199b6dc2bd25937444bbad560bcea29bd"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
 "brotli-decompressor",
]

[[package]]
name = "brotli-decompressor"
version = "4.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74fa05ad7d803d413eb8380983b092cbbaf9a85f151b871360e7b00cd7060b37"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
]

[[package]]
name = "bs58"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf88ba1141d185c399bee5288d850d63b8369520c1eafc32a0430b5b6c287bf4"
dependencies = [
 "tinyvec",
]

[[package]]
name = "bstr"
version = "1.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "531a9155a481e2ee699d4f98f43c0ca4ff8ee1bfd55c31e9e98fb29d2b176fe0"
dependencies = [
 "memchr",
 "regex-automata",
 "serde",
]

[[package]]
name = "bumpalo"
version = "3.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1628fb46dfa0b37568d12e5edd512553eccf6a22a78e8bde00bb4aed84d5bdbf"

[[package]]
name = "bv"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8834bb1d8ee5dc048ee3124f2c7c1afcc6bc9aed03f11e9dfd8c69470a5db340"
dependencies = [
 "feature-probe",
 "serde",
]

[[package]]
name = "bytemuck"
version = "1.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3995eaeebcdf32f91f980d360f78732ddc061097ab4e39991ae7a6ace9194677"
dependencies = [
 "bytemuck_derive",
]

[[package]]
name = "bytemuck_derive"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f154e572231cb6ba2bd1176980827e3d5dc04cc183a75dea38109fbdd672d29"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71b6127be86fdcfddb610f7182ac57211d4b18a3e9c82eb2d17662f2227ad6a"
dependencies = [
 "serde",
]

[[package]]
name = "bzip2"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdb116a6ef3f6c3698828873ad02c3014b3c85cadb88496095628e3ef1e347f8"
dependencies = [
 "bzip2-sys",
 "libc",
]

[[package]]
name = "bzip2-sys"
version = "0.1.13+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "225bff33b2141874fe80d71e07d6eec4f85c5c216453dd96388240f96e1acc14"
dependencies = [
 "cc",
 "pkg-config",
]

[[package]]
name = "caps"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "190baaad529bcfbde9e1a19022c42781bdb6ff9de25721abdb8fd98c0807730b"
dependencies = [
 "libc",
 "thiserror 1.0.69",
]

[[package]]
name = "cc"
version = "1.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be714c154be609ec7f5dad223a33bf1482fff90472de28f7362806e6d4832b8c"
dependencies = [
 "jobserver",
 "libc",
 "shlex",
]

[[package]]
name = "cesu8"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d43a04d8753f35258c91f8ec639f792891f748a1edbd759cf1dcea3382ad83c"

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom",
]

[[package]]
name = "cfg-if"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4785bdd1c96b2a846b2bd7cc02e86b6b3dbf14e7e53446c4f54c92a361040822"

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "cfg_aliases"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613afe47fcd5fac7ccf1db93babcb082c5994d996f20b8b159f2ad1658eb5724"

[[package]]
name = "cfg_eval"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45565fc9416b9896014f5732ac776f810ee53a66730c17e4020c3ec064a8f88f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "chrono"
version = "0.4.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c469d952047f47f91b68d1cba3f10d63c11d73e4636f24f08daf0278abf01c4d"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "serde",
 "wasm-bindgen",
 "windows-link",
]

[[package]]
name = "chrono-humanize"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "799627e6b4d27827a814e837b9d8a504832086081806d45b1afa34dc982b023b"
dependencies = [
 "chrono",
]

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
]

[[package]]
name = "clang-sys"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b023947811758c97c59bf9d1c188fd619ad4718dcaa767947df1cadb14f39f4"
dependencies = [
 "glob",
 "libc",
]

[[package]]
name = "clap"
version = "2.34.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0610544180c38b88101fecf2dd634b174a62eef6946f84dfc6a7127512b381c"
dependencies = [
 "ansi_term",
 "atty",
 "bitflags 1.3.2",
 "strsim 0.8.0",
 "textwrap 0.11.0",
 "unicode-width 0.1.14",
 "vec_map",
]

[[package]]
name = "clap"
version = "3.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ea181bf566f71cb9a5d17a59e1871af638180a18fb0035c92ae62b705207123"
dependencies = [
 "atty",
 "bitflags 1.3.2",
 "clap_lex 0.2.4",
 "indexmap 1.9.3",
 "once_cell",
 "strsim 0.10.0",
 "termcolor",
 "textwrap 0.16.2",
]

[[package]]
name = "clap"
version = "4.5.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "027bb0d98429ae334a8698531da7077bdf906419543a35a55c2cb1b66437d767"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.5.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5589e0cba072e0f3d23791efac0fd8627b49c829c196a492e88168e6a669d863"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex 0.7.4",
 "strsim 0.11.1",
]

[[package]]
name = "clap_derive"
version = "4.5.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf4ced95c6f4a675af3da73304b9ac4ed991640c36374e4b46795c49e17cf1ed"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "clap_lex"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2850f2f5a82cbf437dd5af4d49848fbdfc27c157c3d010345776f952765261c5"
dependencies = [
 "os_str_bytes",
]

[[package]]
name = "clap_lex"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46ad14479a25103f283c0f10005961cf086d8dc42205bb44c46ac563475dca6"

[[package]]
name = "colorchoice"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b63caa9aa9397e2d9480a9b13673856c78d8ac123288526c37d7839f2a86990"

[[package]]
name = "combine"
version = "3.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da3da6baa321ec19e1cc41d31bf599f00c783d0517095cdaf0332e3fe8d20680"
dependencies = [
 "ascii",
 "byteorder",
 "either",
 "memchr",
 "unreachable",
]

[[package]]
name = "combine"
version = "4.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba5a308b75df32fe02788e748662718f03fde005016435c444eea572398219fd"
dependencies = [
 "bytes",
 "memchr",
]

[[package]]
name = "concurrent-queue"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ca0197aee26d1ae37445ee532fefce43251d24cc7c166799f4d46817f1d3973"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "conditional-mod"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67935045d95e19071aae6ee98d649f2a5593e510802040c622200c8d6666a9ca"

[[package]]
name = "console"
version = "0.15.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "054ccb5b10f9f2cbf51eb355ca1d05c2d279ce1804688d0db74b4733a5aeafd8"
dependencies = [
 "encode_unicode",
 "libc",
 "once_cell",
 "unicode-width 0.2.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "console"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e09ced7ebbccb63b4c65413d821f2e00ce54c5ca4514ddc6b3c892fdbcbc69d"
dependencies = [
 "encode_unicode",
 "libc",
 "once_cell",
 "unicode-width 0.2.0",
 "windows-sys 0.60.2",
]

[[package]]
name = "console_error_panic_hook"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06aeb73f470f66dcdbf7223caeebb85984942f22f1adb2a088cf9668146bbbc"
dependencies = [
 "cfg-if 1.0.0",
 "wasm-bindgen",
]

[[package]]
name = "console_log"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89f72f65e8501878b8a004d5a1afb780987e2ce2b4532c562e367a72c57499f"
dependencies = [
 "log",
 "web-sys",
]

[[package]]
name = "constant_time_eq"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c74b8349d32d297c9134b8c88677813a227df8f779daa29bfc29c183fe3dca6"

[[package]]
name = "convert_case"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6245d59a3e82a7fc217c5828a6692dbc6dfb63a0c8c90495621f7b9d79704a0e"

[[package]]
name = "convert_case"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec182b0ca2f35d8fc196cf3404988fd8b8c739a4d270ff118a398feb0cbec1ca"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "core-error"
version = "0.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efcdb2972eb64230b4c50646d8498ff73f5128d196a90c7236eec4cbe8619b8f"
dependencies = [
 "version_check",
]

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2a6cd9ae233e7f62ba4e9353e81a88df7fc8a5987b8d445b4d90c879bd156f6"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773648b94d0e5d620f64f280777445740e61fe701025087ec8b57f45c791888b"

[[package]]
name = "core_affinity"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f8a03115cc34fb0d7c321dd154a3914b3ca082ccc5c11d91bf7117dbbe7171f"
dependencies = [
 "kernel32-sys",
 "libc",
 "num_cpus",
 "winapi 0.2.8",
]

[[package]]
name = "cpufeatures"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59ed5838eebb26a2bb2e58f6d5b5316989ae9d08bab10e0e6d103e656d1b0280"
dependencies = [
 "libc",
]

[[package]]
name = "crc32fast"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97769d94ddab943e4510d138150169a2758b5ef3eb191a9ee688de3e23ef7b3"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82b8f8f868b36967f9606790d1903570de9ceaf870a7bf9fbbd3016d636a2cb2"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd111b7b7f7d55b72c0a6ae361660ee5853c9af73f70c3c2ef6858b950e2e51"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a5c400df2834b80a4c3327b3aad3a4c4cd4de0629063962b03235697506a28"

[[package]]
name = "crunchy"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43da5946c66ffcc7745f48db692ffbb10a83bfe0afd96235c5c2a4fb23994929"

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "crypto-mac"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b584a330336237c1eecd3e94266efb216c56ed91225d634cb2991c5f3fd1aeab"
dependencies = [
 "generic-array",
 "subtle",
]

[[package]]
name = "ctr"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0369ee1ad671834580515889b80f2ea915f23b8be8d0daa4bbaf2ac5c7590835"
dependencies = [
 "cipher",
]

[[package]]
name = "curve25519-dalek"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b9fdf9972b2bd6af2d913799d9ebc165ea4d2e65878e329d9c6b372c4491b61"
dependencies = [
 "byteorder",
 "digest 0.9.0",
 "rand_core 0.5.1",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek"
version = "4.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fb8b7c4503de7d6ae7b42ab72a5a59857b4c937ec27a3d4539dba95b5ab2be"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "curve25519-dalek-derive",
 "digest 0.10.7",
 "fiat-crypto",
 "rand_core 0.6.4",
 "rustc_version",
 "serde",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek-derive"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46882e17999c6cc590af592290432be3bce0428cb0d5f8b6715e4dc7b383eb3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "darling"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f63b86c8a8826a49b8c21f08a2d07338eec8d900540f8630dc76284be802989"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95133861a8032aaea082871032f5815eb9e98cef03fa916ab4500513994df9e5"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim 0.11.1",
 "syn 2.0.99",
]

[[package]]
name = "darling_macro"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d336a2a514f6ccccaa3e09b02d41d35330c07ddf03a62165fcec10bb561c7806"
dependencies = [
 "darling_core",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "dashmap"
version = "5.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "978747c1d849a7d2ee5e8adc0159961c48fb7e5db2f06af6723b80123bb53856"
dependencies = [
 "cfg-if 1.0.0",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core 0.9.10",
 "rayon",
]

[[package]]
name = "data-encoding"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "575f75dfd25738df5b91b8e43e14d44bda14637a58fae779fd2b064f8bf3e010"

[[package]]
name = "der-parser"
version = "8.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbd676fbbab537128ef0278adb5576cf363cff6aa22a7b24effe97347cfab61e"
dependencies = [
 "asn1-rs",
 "displaydoc",
 "nom",
 "num-bigint 0.4.6",
 "num-traits",
 "rusticata-macros",
]

[[package]]
name = "deranged"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b42b6fa04a440b495c8b04d0e71b707c585f83cb9cb28cf8cd0d976c315e31b4"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derivation-path"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e5c37193a1db1d8ed868c03ec7b152175f26160a5b740e5e484143877e0adf0"

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive-where"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "510c292c8cf384b1a340b816a9a6cf2599eb8f566a44949024af88418000c50b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "derive_more"
version = "0.99.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3da29a38df43d6f156149c9b43ded5e018ddff2a855cf2cfd62e8cd7d079c69f"
dependencies = [
 "convert_case 0.4.0",
 "proc-macro2",
 "quote",
 "rustc_version",
 "syn 2.0.99",
]

[[package]]
name = "derive_more"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a9b99b9cbbe49445b21764dc0625032a89b145a2642e67603e1c936f5458d05"
dependencies = [
 "derive_more-impl",
]

[[package]]
name = "derive_more-impl"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7330aeadfbe296029522e6c40f315320aba36fc43a5b3632f3795348f3bd22"
dependencies = [
 "convert_case 0.6.0",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
 "unicode-xid",
]

[[package]]
name = "dialoguer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59c6f2989294b9a498d3ad5491a79c6deb604617378e1cdc4bfc1c1361fe2f87"
dependencies = [
 "console 0.15.11",
 "shell-words",
 "tempfile",
 "zeroize",
]

[[package]]
name = "difflib"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6184e33543162437515c2e2b48714794e37845ec9851711914eec9d308f6ebe8"

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer 0.10.4",
 "crypto-common",
 "subtle",
]

[[package]]
name = "dir-diff"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7ad16bf5f84253b50d6557681c58c3ab67c47c77d39fed9aeb56e947290bd10"
dependencies = [
 "walkdir",
]

[[package]]
name = "dirs-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b98cf8ebf19c3d1b223e151f99a4f9f0690dca41414773390fc824184ac833e1"
dependencies = [
 "cfg-if 1.0.0",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc",
 "redox_users",
 "winapi 0.3.9",
]

[[package]]
name = "displaydoc"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97369cbbc041bc366949bc74d34658d6cda5621039731c6310521892a3a20ae0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "dlopen2"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09b4f5f101177ff01b8ec4ecc81eead416a8aa42819a2869311b3420fa114ffa"
dependencies = [
 "dlopen2_derive",
 "libc",
 "once_cell",
 "winapi 0.3.9",
]

[[package]]
name = "dlopen2_derive"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a6cbae11b3de8fce2a456e8ea3dada226b35fe791f0dc1d360c0941f0bb681f3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "doc-comment"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea41bba32d969b513997752735605054bc0dfa92b4c56bf1189f2e174be7a10"

[[package]]
name = "downcast"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1435fa1053d8b2fbbe9be7e97eca7f33d37b28409959813daefc1446a14247f1"

[[package]]
name = "dyn-clone"
version = "1.0.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c7a8fb8a9fbf66c1f703fe16184d10ca0ee9d23be5b4436400408ba54a95005"

[[package]]
name = "eager"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "abe71d579d1812060163dff96056261deb5bf6729b100fa2e36a68b9649ba3d3"

[[package]]
name = "ed25519"
version = "1.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91cff35c70bba8a626e3185d8cd48cc11b5437e1a5bcd15b9b5fa3c64b6dfee7"
dependencies = [
 "signature",
]

[[package]]
name = "ed25519-dalek"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c762bae6dcaf24c4c84667b8579785430908723d5c889f469d76a41d59cc7a9d"
dependencies = [
 "curve25519-dalek 3.2.0",
 "ed25519",
 "rand 0.7.3",
 "serde",
 "sha2 0.9.9",
 "zeroize",
]

[[package]]
name = "ed25519-dalek-bip32"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d2be62a4061b872c8c0873ee4fc6f101ce7b889d039f019c5fa2af471a59908"
dependencies = [
 "derivation-path",
 "ed25519-dalek",
 "hmac 0.12.1",
 "sha2 0.10.9",
]

[[package]]
name = "educe"
version = "0.4.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f0042ff8246a363dbe77d2ceedb073339e85a804b9a47636c6e016a9a32c05f"
dependencies = [
 "enum-ordinalize",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"

[[package]]
name = "encode_unicode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34aa73646ffb006b8f5147f3dc182bd4bcb190227ce861fc4a4844bf8e3cb2c0"

[[package]]
name = "encoding_rs"
version = "0.8.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75030f3c4f45dafd7586dd6780965a8c7e8e285a5ecb86713e63a79c5b2766f3"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "enum-iterator"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fd242f399be1da0a5354aa462d57b4ab2b4ee0683cc552f7c007d2d12d36e94"
dependencies = [
 "enum-iterator-derive",
]

[[package]]
name = "enum-iterator-derive"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1ab991c1362ac86c61ab6f556cff143daa22e5a15e4e189df818b2fd19fe65b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "enum-ordinalize"
version = "3.1.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bf1fa3f06bbff1ea5b1a9c7b14aa992a39657db60a2759457328d7e058f49ee"
dependencies = [
 "num-bigint 0.4.6",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "env_filter"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "186e05a59d4c50738528153b83b0b0194d3a29507dfec16eccd4b342903397d0"
dependencies = [
 "log",
 "regex",
]

[[package]]
name = "env_logger"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a12e6657c4c97ebab115a42dcee77225f7f482cdd841cf7088c657a42e9e00e7"
dependencies = [
 "atty",
 "humantime",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "env_logger"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c863f0904021b108aa8b2f55046443e6b1ebde8fd4a15c399893aae4fa069f"
dependencies = [
 "anstream",
 "anstyle",
 "env_filter",
 "jiff",
 "log",
]

[[package]]
name = "equivalent"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "877a4ace8713b0bcf2a4e7eec82529c029f1d0619886d18145fea96c3ffe5c0f"

[[package]]
name = "errno"
version = "0.3.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33d852cb9b869c2a9b3df2f71a3074817f01e1844f839a144f5fcef059a4eb5d"
dependencies = [
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "escape8259"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5692dd7b5a1978a5aeb0ce83b7655c58ca8efdcb79d21036ea249da95afec2c6"

[[package]]
name = "etcd-client"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4b0ea5ef6dc2388a4b1669fa32097249bc03a15417b97cb75e38afb309e4a89"
dependencies = [
 "http 0.2.12",
 "prost",
 "tokio",
 "tokio-stream",
 "tonic",
 "tonic-build",
 "tower 0.4.13",
 "tower-service",
]

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "event-listener"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3492acde4c3fc54c845eaab3eed8bd00c7a7d881f78bfc801e43a93dec1331ae"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c3e4e0dd3673c1139bf041f3008816d9cf2946bbfac2945c09e523b8d7b05b2"
dependencies = [
 "event-listener 5.4.0",
 "pin-project-lite",
]

[[package]]
name = "fast-math"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2465292146cdfc2011350fe3b1c616ac83cf0faeedb33463ba1c332ed8948d66"
dependencies = [
 "ieee754",
]

[[package]]
name = "fastbloom"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "27cea6e7f512d43b098939ff4d5a5d6fe3db07971e1d05176fe26c642d33f5b8"
dependencies = [
 "getrandom 0.3.1",
 "rand 0.9.1",
 "siphasher 1.0.1",
 "wide",
]

[[package]]
name = "fastrand"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37909eebbb50d72f9059c3b6d82c0463f2ff062c9e95845c43a6c9c0355411be"

[[package]]
name = "feature-probe"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835a3dc7d1ec9e75e2b5fb4ba75396837112d2060b03f7d43bc1897c7f7211da"

[[package]]
name = "fiat-crypto"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28dea519a9695b9977216879a3ebfddf92f1c08c05d984f8996aecd6ecdc811d"

[[package]]
name = "filetime"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35c0522e981e68cbfa8c3f978441a5f34b30b96e146b33cd3359176b50fe8586"
dependencies = [
 "cfg-if 1.0.0",
 "libc",
 "libredox",
 "windows-sys 0.59.0",
]

[[package]]
name = "five8"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75b8549488b4715defcb0d8a8a1c1c76a80661b5fa106b4ca0e7fce59d7d875"
dependencies = [
 "five8_core",
]

[[package]]
name = "five8_const"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b4f62f0f8ca357f93ae90c8c2dd1041a1f665fde2f889ea9b1787903829015"
dependencies = [
 "five8_core",
]

[[package]]
name = "five8_core"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94474d15a76982be62ca8a39570dccce148d98c238ebb7408b0a21b2c4bdddc4"

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "flate2"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11faaf5a5236997af9848be0bef4db95824b1d534ebc64d0f0c6cf3e67bd38dc"
dependencies = [
 "crc32fast",
 "miniz_oxide",
]

[[package]]
name = "float-cmp"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98de4bbd547a563b716d8dfa9aad1cb19bfab00f4fa09a6a4ed21dbcf44ce9c4"
dependencies = [
 "num-traits",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0d2fde1f7b3d48b8395d5f2de76c18a528bd6a9cdde438df747bfcba3e05d6f"

[[package]]
name = "foreign-types"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"
dependencies = [
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-shared"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding 2.3.1",
]

[[package]]
name = "fragile"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c2141d6d6c8512188a7891b4b01590a45f6dac67afb4f255c4124dbb86d4eaa"

[[package]]
name = "fs_extra"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42703706b716c37f96a77aea830392ad231f44c9e9a67872fa5548707e11b11c"

[[package]]
name = "futures"
version = "0.1.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a471a38ef8ed83cd6e40aa59c1ffe17db6855c18e3604d9c4ed8c08ebc28678"

[[package]]
name = "futures"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65bc07b1a8bc7c85c5f2e110c476c7389b4554ba72af57d8445ea63a576b0876"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dff15bf788c671c1934e366d07e30c1814a8ef514e1af724a602e8a2fbe1b10"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f29059c0c2090612e8d742178b0580d2dc940c837851ad723096f87af6663e"

[[package]]
name = "futures-executor"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e28d1d997f585e54aebc3f97d39e72338912123a67330d723fdbb564d646c9f"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-io"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e5c1b78ca4aae1ac06c48a526a655760685149f0d465d21f37abfe57ce075c6"

[[package]]
name = "futures-macro"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "162ee34ebcb7c64a8abebc059ce0fee27c2262618d7b60ed8faf72fef13c3650"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "futures-sink"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e575fab7d1e0dcb8d0c7bcf9a63ee213816ab51902e6d244a95819acacf1d4f7"

[[package]]
name = "futures-task"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f90f7dce0722e95104fcb095585910c0977252f286e354b5e3bd38902cd99988"

[[package]]
name = "futures-timer"
version = "3.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f288b0a4f20f9a56b5d1da57e2227c661b7b16168e2f72365f57b63326e29b24"

[[package]]
name = "futures-util"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fa08315bb612088cc391249efdc3bc77536f16c91f6cf495e6fbe85b20a4a81"
dependencies = [
 "futures 0.1.31",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "typenum",
 "version_check",
]

[[package]]
name = "gethostname"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1ebd34e35c46e00bb73e81363248d627782724609fe1b6396f553f68fe3862e"
dependencies = [
 "libc",
 "winapi 0.3.9",
]

[[package]]
name = "getrandom"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fc3cb4d91f53b50155bdcfd23f6a4c39ae1969c2ae85982b135750cccaf5fce"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "libc",
 "wasi 0.9.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4567c8db10ae91089c99af84c68c38da3ec2f087c3f82960bcdbf3656b6f4d7"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a49c392881ce6d5c3b8cb70f98717b7c07aabbdff06687b9030dbfbe2725f8"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "libc",
 "wasi 0.13.3+wasi-0.2.2",
 "wasm-bindgen",
 "windows-targets 0.52.6",
]

[[package]]
name = "gimli"
version = "0.31.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07e28edb80900c19c28f1072f2e8aeca7fa06b23cd4169cefe1af5aa3260783f"

[[package]]
name = "glob"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d1add55171497b4705a648c6b583acafb01d58050a51727785f0b2c8e0a2b2"

[[package]]
name = "globset"
version = "0.4.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54a1028dfc5f5df5da8a56a73e6c153c9a9708ec57232470703592a3f18e49f5"
dependencies = [
 "aho-corasick",
 "bstr",
 "log",
 "regex-automata",
 "regex-syntax",
]

[[package]]
name = "goauth"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8af59a261bcf42f45d1b261232847b9b850ba0a1419d6100698246fb66e9240"
dependencies = [
 "arc-swap",
 "futures 0.3.31",
 "log",
 "reqwest 0.11.27",
 "serde",
 "serde_derive",
 "serde_json",
 "simpl",
 "smpl_jwt",
 "time",
 "tokio",
]

[[package]]
name = "governor"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68a7f542ee6b35af73b06abc0dad1c1bae89964e4e253bc4b587b91c9637867b"
dependencies = [
 "cfg-if 1.0.0",
 "dashmap",
 "futures 0.3.31",
 "futures-timer",
 "no-std-compat",
 "nonzero_ext",
 "parking_lot 0.12.3",
 "portable-atomic",
 "quanta",
 "rand 0.8.5",
 "smallvec",
 "spinning_top",
]

[[package]]
name = "h2"
version = "0.3.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81fe527a889e1532da5c525686d96d4c2e74cdd345badf8dfef9f6b39dd5f5e8"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http 0.2.12",
 "indexmap 2.10.0",
 "slab",
 "tokio",
 "tokio-util 0.7.15",
 "tracing",
]

[[package]]
name = "hash32"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47d60b12902ba28e2730cd37e95b8c9223af2808df9e902d4df49588d1470606"
dependencies = [
 "byteorder",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"
dependencies = [
 "ahash 0.7.8",
]

[[package]]
name = "hashbrown"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a3c133739dddd0d2990f9a4bdf8eb4b21ef50e4851ca85ab661199821d510e"
dependencies = [
 "ahash 0.8.11",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"

[[package]]
name = "hashbrown"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf151400ff0baff5465007dd2f3e717f3fe502074ca563069ce3a6629d07b289"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
]

[[package]]
name = "headers"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06683b93020a07e3dbcf5f8c0f6d40080d725bea7936fc01ad345c01b97dc270"
dependencies = [
 "base64 0.21.7",
 "bytes",
 "headers-core",
 "http 0.2.12",
 "httpdate",
 "mime",
 "sha1",
]

[[package]]
name = "headers-core"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7f66481bfee273957b1f20485a4ff3362987f85b2c236580d81b4eb7a326429"
dependencies = [
 "http 0.2.12",
]

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.1.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62b467343b94ba476dcb2500d242dadbb39557df889310ac77c5d99100aaac33"
dependencies = [
 "libc",
]

[[package]]
name = "hermit-abi"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc0fef456e4baa96da950455cd02c081ca953b141298e41db3fc7e36b1da849c"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"

[[package]]
name = "hidapi"
version = "2.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03b876ecf37e86b359573c16c8366bc3eba52b689884a0fc42ba3f67203d2a8b"
dependencies = [
 "cc",
 "cfg-if 1.0.0",
 "libc",
 "pkg-config",
 "windows-sys 0.48.0",
]

[[package]]
name = "histogram"
version = "0.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12cb882ccb290b8646e554b157ab0b71e64e8d5bef775cd66b6531e52d302669"

[[package]]
name = "hmac"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126888268dcc288495a26bf004b38c5fdbb31682f992c84ceb046a1f0fe38840"
dependencies = [
 "crypto-mac",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "hmac-drbg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17ea0a1394df5b6574da6e0c1ade9e78868c9fb0a4e5ef4428e32da4676b85b1"
dependencies = [
 "digest 0.9.0",
 "generic-array",
 "hmac 0.8.1",
]

[[package]]
name = "home"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589533453244b0995c858700322199b2becb13b627df2851f64a2775d024abcf"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "http"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "601cbb57e577e2f5ef5be8e7b83f0f63994f25aa94d673e54a92d5c516d101f1"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4a85d31aea989eead29a3aaf9e1115a180df8282431156e533de47660892565"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ceab25649e9960c0311ea418d17bee82c0dcec1bd053b5f9a66e265a693bed2"
dependencies = [
 "bytes",
 "http 0.2.12",
 "pin-project-lite",
]

[[package]]
name = "http-body"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1efedce1fb8e6913f23e0c92de8e62cd5b772a67e7b3946df930a62566c93184"
dependencies = [
 "bytes",
 "http 1.3.1",
]

[[package]]
name = "http-body-util"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b021d93e26becf5dc7e1b75b1bed1fd93124b374ceb73f43d4d4eafec896a64a"
dependencies = [
 "bytes",
 "futures-core",
 "http 1.3.1",
 "http-body 1.0.1",
 "pin-project-lite",
]

[[package]]
name = "httparse"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dbf3de79e51f3d586ab4cb9d5c3e2c14aa28ed23d180cf89b4df0454a69cc87"

[[package]]
name = "httpdate"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df3b46402a9d5adb4c86a0cf463f42e19994e3ee891101b1841f30a545cb49a9"

[[package]]
name = "humantime"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b112acc8b3adf4b107a8ec20977da0273a8c386765a3ec0229bd500a1443f9f"

[[package]]
name = "hyper"
version = "0.14.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41dfc780fdec9373c01bae43289ea34c972e40ee3c9f6b3c8801a35f35586ce7"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http 0.2.12",
 "http-body 0.4.6",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "socket2 0.5.10",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc2b571658e38e0c01b1fdca3bbbe93c00d3d71693ff2770043f8c29bc7d6f80"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "httparse",
 "itoa",
 "pin-project-lite",
 "smallvec",
 "tokio",
 "want",
]

[[package]]
name = "hyper-proxy"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca815a891b24fdfb243fa3239c86154392b0953ee584aa1a2a1f66d20cbe75cc"
dependencies = [
 "bytes",
 "futures 0.3.31",
 "headers",
 "http 0.2.12",
 "hyper 0.14.32",
 "hyper-tls",
 "native-tls",
 "tokio",
 "tokio-native-tls",
 "tower-service",
]

[[package]]
name = "hyper-rustls"
version = "0.27.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3c93eb611681b207e1fe55d5a71ecf91572ec8a6705cdb6857f7d8d5242cf58"
dependencies = [
 "http 1.3.1",
 "hyper 1.6.0",
 "hyper-util",
 "rustls 0.23.29",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls 0.26.2",
 "tower-service",
 "webpki-roots 1.0.1",
]

[[package]]
name = "hyper-timeout"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbb958482e8c7be4bc3cf272a766a2b0bf1a6755e7a6ae777f017a31d11b13b1"
dependencies = [
 "hyper 0.14.32",
 "pin-project-lite",
 "tokio",
 "tokio-io-timeout",
]

[[package]]
name = "hyper-tls"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6183ddfa99b85da61a140bea0efc93fdf56ceaa041b37d553518030827f9905"
dependencies = [
 "bytes",
 "hyper 0.14.32",
 "native-tls",
 "tokio",
 "tokio-native-tls",
]

[[package]]
name = "hyper-util"
version = "0.1.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f66d5bd4c6f02bf0542fad85d626775bab9258cf795a4256dcaf3161114d1df"
dependencies = [
 "base64 0.22.1",
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "hyper 1.6.0",
 "ipnet",
 "libc",
 "percent-encoding 2.3.1",
 "pin-project-lite",
 "socket2 0.5.10",
 "tokio",
 "tower-service",
 "tracing",
]

[[package]]
name = "iana-time-zone"
version = "0.1.61"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "235e081f3925a06703c2d0117ea8b91f042756fd6e7a6e5d901e8ca1a996b220"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "wasm-bindgen",
 "windows-core",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "icu_collections"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fa452206ebee18c4b5c2274dbf1de17008e874b4dc4f0aea9d01ca79e4526"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locid"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13acbb8371917fc971be86fc8057c41a64b521c184808a698c02acc242dbf637"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_locid_transform"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01d11ac35de8e40fdeda00d9e1e9d92525f3f9d887cdd7aa81d727596788b54e"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_locid_transform_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_locid_transform_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdc8ff3388f852bede6b579ad4e978ab004f139284d7b28715f773507b946f6e"

[[package]]
name = "icu_normalizer"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19ce3e0da2ec68599d193c93d088142efd7f9c5d6fc9b803774855747dc6a84f"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "utf16_iter",
 "utf8_iter",
 "write16",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8cafbf7aa791e9b22bec55a167906f9e1215fd475cd22adfcf660e03e989516"

[[package]]
name = "icu_properties"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93d6020766cfc6302c15dbbc9c8778c37e62c14427cb7f6e601d849e092aeef5"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locid_transform",
 "icu_properties_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67a8effbc3dd3e4ba1afa8ad918d5684b8868b3b26500753effea8d2eed19569"

[[package]]
name = "icu_provider"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ed421c8a8ef78d3e2dbc98a973be2f3770cb42b606e3ab18d6237c4dfde68d9"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_provider_macros",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_provider_macros"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ec89e9337638ecdc08744df490b221a7399bf8d164eb52a665454e60e075ad6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38f09e0f0b1fb55fdee1f17470ad800da77af5186a1a76c026b679358b7e844e"
dependencies = [
 "matches",
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daca1df1c957320b2cf139ac61e7bd64fed304c5040df000a745aa1de3b4ef71"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "ieee754"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9007da9cacbd3e6343da136e98b0d2df013f553d35bdec8b518f07bea768e19c"

[[package]]
name = "im"
version = "15.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0acd33ff0285af998aaf9b57342af478078f53492322fafc47450e09397e0e9"
dependencies = [
 "bitmaps",
 "rand_core 0.6.4",
 "rand_xoshiro",
 "rayon",
 "serde",
 "sized-chunks",
 "typenum",
 "version_check",
]

[[package]]
name = "include_dir"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "923d117408f1e49d914f1a379a309cffe4f18c05cf4e3d12e613a15fc81bd0dd"
dependencies = [
 "include_dir_macros",
]

[[package]]
name = "include_dir_macros"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cab85a7ed0bd5f0e76d93846e0147172bed2e2d3f859bcc33a8d9699cad1a75"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "indexmap"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd070e393353796e801d209ad339e89596eb4c8d430d18ede6a1cced8fafbd99"
dependencies = [
 "autocfg",
 "hashbrown 0.12.3",
 "serde",
]

[[package]]
name = "indexmap"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe4cd85333e22411419a0bcae1297d25e58c9443848b11dc6a86fefe8c78a661"
dependencies = [
 "equivalent",
 "hashbrown 0.15.2",
 "rayon",
 "serde",
]

[[package]]
name = "indicatif"
version = "0.17.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "183b3088984b400f4cfac3620d5e076c84da5364016b4f49473de574b2586235"
dependencies = [
 "console 0.15.11",
 "number_prefix",
 "portable-atomic",
 "unicode-width 0.2.0",
 "web-time",
]

[[package]]
name = "inout"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "879f10e63c20629ecabbb64a8010319738c66a5cd0c29b02d63d272b03751d01"
dependencies = [
 "generic-array",
]

[[package]]
name = "instant"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0242819d153cba4b4b05a5a8f2a7e9bbf97b6055b2a002b395c96b5ff3c0222"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "io-uring"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b86e202f00093dcba4275d4636b93ef9dd75d025ae560d2521b45ea28ab49013"
dependencies = [
 "bitflags 2.9.1",
 "cfg-if 1.0.0",
 "libc",
]

[[package]]
name = "ipnet"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "469fb0b9cefa57e3ef31275ee7cacb78f2fdca44e4765491884a2b119d4eb130"

[[package]]
name = "iri-string"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbc5ebe9c3a1a7a5127f920a418f7585e9e758e911d0466ed004f393b0e380b2"
dependencies = [
 "memchr",
 "serde",
]

[[package]]
name = "is_terminal_polyfill"
version = "1.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7943c866cc5cd64cbc25b2e01621d07fa8eb2a1a23160ee81ce38704e97b8ecf"

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a5f13b858c8d314ee3e8f639011f7ccefe71f97f96e50151fb991f267928e2c"

[[package]]
name = "jiff"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be1f93b8b1eb69c77f24bbb0afdf66f54b632ee39af40ca21c4365a1d7347e49"
dependencies = [
 "jiff-static",
 "log",
 "portable-atomic",
 "portable-atomic-util",
 "serde",
]

[[package]]
name = "jiff-static"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03343451ff899767262ec32146f6d559dd759fdadf42ff0e227c7c48f72594b4"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "jni"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a87aa2bb7d2af34197c04845522473242e1aa17c12f4935d5856491a7fb8c97"
dependencies = [
 "cesu8",
 "cfg-if 1.0.0",
 "combine 4.6.7",
 "jni-sys",
 "log",
 "thiserror 1.0.69",
 "walkdir",
 "windows-sys 0.45.0",
]

[[package]]
name = "jni-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8eaf4bc02d17cbdd7ff4c7438cafcdf7fb9a4613313ad11b4f8fefe7d3fa0130"

[[package]]
name = "jobserver"
version = "0.1.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48d1dbcbbeb6a7fec7e059840aa538bd62aaccf972c7346c4d9d2059312853d0"
dependencies = [
 "libc",
]

[[package]]
name = "js-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cfaf33c695fc6e08064efbc1f72ec937429614f25eef83af942d0e227c3a28f"
dependencies = [
 "once_cell",
 "wasm-bindgen",
]

[[package]]
name = "json5"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96b0db21af676c1ce64250b5f40f3ce2cf27e4e47cb91ed91eb6fe9350b430c1"
dependencies = [
 "pest",
 "pest_derive",
 "serde",
]

[[package]]
name = "jsonrpc-client-transports"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2b99d4207e2a04fb4581746903c2bb7eb376f88de9c699d0f3e10feeac0cd3a"
dependencies = [
 "derive_more 0.99.19",
 "futures 0.3.31",
 "jsonrpc-core",
 "jsonrpc-pubsub",
 "log",
 "serde",
 "serde_json",
 "url 1.7.2",
]

[[package]]
name = "jsonrpc-core"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14f7f76aef2d054868398427f6c54943cf3d1caa9a7ec7d0c38d69df97a965eb"
dependencies = [
 "futures 0.3.31",
 "futures-executor",
 "futures-util",
 "log",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "jsonrpc-core-client"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b51da17abecbdab3e3d4f26b01c5ec075e88d3abe3ab3b05dc9aa69392764ec0"
dependencies = [
 "futures 0.3.31",
 "jsonrpc-client-transports",
]

[[package]]
name = "jsonrpc-derive"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b939a78fa820cdfcb7ee7484466746a7377760970f6f9c6fe19f9edcc8a38d2"
dependencies = [
 "proc-macro-crate 0.1.5",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "jsonrpc-http-server"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1dea6e07251d9ce6a552abfb5d7ad6bc290a4596c8dcc3d795fae2bbdc1f3ff"
dependencies = [
 "futures 0.3.31",
 "hyper 0.14.32",
 "jsonrpc-core",
 "jsonrpc-server-utils",
 "log",
 "net2",
 "parking_lot 0.11.2",
 "unicase",
]

[[package]]
name = "jsonrpc-pubsub"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240f87695e6c6f62fb37f05c02c04953cf68d6408b8c1c89de85c7a0125b1011"
dependencies = [
 "futures 0.3.31",
 "jsonrpc-core",
 "lazy_static",
 "log",
 "parking_lot 0.11.2",
 "rand 0.7.3",
 "serde",
]

[[package]]
name = "jsonrpc-server-utils"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa4fdea130485b572c39a460d50888beb00afb3e35de23ccd7fad8ff19f0e0d4"
dependencies = [
 "bytes",
 "futures 0.3.31",
 "globset",
 "jsonrpc-core",
 "lazy_static",
 "log",
 "tokio",
 "tokio-stream",
 "tokio-util 0.6.10",
 "unicase",
]

[[package]]
name = "kaigan"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ba15de5aeb137f0f65aa3bf82187647f1285abfe5b20c80c2c37f7007ad519a"
dependencies = [
 "borsh 0.10.4",
 "serde",
]

[[package]]
name = "keccak"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecc2af9a1119c51f12a14607e783cb977bde58bc069ff0c3da1095e635d70654"
dependencies = [
 "cpufeatures",
]

[[package]]
name = "kernel32-sys"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7507624b29483431c0ba2d82aece8ca6cdba9382bff4ddd0f7490560c056098d"
dependencies = [
 "winapi 0.2.8",
 "winapi-build",
]

[[package]]
name = "lazy-lru"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a35523c6dfa972e1fd19132ef647eff4360a6546c6271807e1327ca6e8797f96"
dependencies = [
 "hashbrown 0.15.2",
]

[[package]]
name = "lazy_static"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbd2bcb4c963f2ddae06a2efc7e9f3591312473c50c6685e1f298068316e66fe"

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "libc"
version = "0.2.174"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1171693293099992e19cddea4e8b849964e9846f4acee11b3948bcc337be8776"

[[package]]
name = "libloading"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b67380fd3b2fbe7527a606e18729d21c6f3951633d0500574c4dc22d2d638b9f"
dependencies = [
 "cfg-if 1.0.0",
 "winapi 0.3.9",
]

[[package]]
name = "libm"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8355be11b20d696c8f18f6cc018c4e372165b1fa8126cef092399c9951984ffa"

[[package]]
name = "libredox"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0ff37bd590ca25063e35af745c343cb7a0271906fb7b37e4813e8f79f00268d"
dependencies = [
 "bitflags 2.9.1",
 "libc",
 "redox_syscall 0.5.10",
]

[[package]]
name = "librocksdb-sys"
version = "0.17.1+9.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b7869a512ae9982f4d46ba482c2a304f1efd80c6412a3d4bf57bb79a619679f"
dependencies = [
 "bindgen",
 "bzip2-sys",
 "cc",
 "libc",
 "libz-sys",
 "lz4-sys",
]

[[package]]
name = "libsecp256k1"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9d220bc1feda2ac231cb78c3d26f27676b8cf82c96971f7aeef3d0cf2797c73"
dependencies = [
 "arrayref",
 "base64 0.12.3",
 "digest 0.9.0",
 "hmac-drbg",
 "libsecp256k1-core",
 "libsecp256k1-gen-ecmult",
 "libsecp256k1-gen-genmult",
 "rand 0.7.3",
 "serde",
 "sha2 0.9.9",
 "typenum",
]

[[package]]
name = "libsecp256k1-core"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0f6ab710cec28cef759c5f18671a27dae2a5f952cdaaee1d8e2908cb2478a80"
dependencies = [
 "crunchy",
 "digest 0.9.0",
 "subtle",
]

[[package]]
name = "libsecp256k1-gen-ecmult"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccab96b584d38fac86a83f07e659f0deafd0253dc096dab5a36d53efe653c5c3"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libsecp256k1-gen-genmult"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67abfe149395e3aa1c48a2beb32b068e2334402df8181f818d3aee2b304c4f5d"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libtest-mimic"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5297962ef19edda4ce33aaa484386e0a5b3d7f2f4e037cbeee00503ef6b29d33"
dependencies = [
 "anstream",
 "anstyle",
 "clap 4.5.31",
 "escape8259",
]

[[package]]
name = "libz-sys"
version = "1.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df9b68e50e6e0b26f672573834882eb57759f6db9b3be2ea3c35c91188bb4eaa"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "light-poseidon"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c9a85a9752c549ceb7578064b4ed891179d20acd85f27318573b64d2d7ee7ee"
dependencies = [
 "ark-bn254",
 "ark-ff",
 "num-bigint 0.4.6",
 "thiserror 1.0.69",
]

[[package]]
name = "linux-raw-sys"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d26c52dbd32dccf2d10cac7725f8eae5296885fb5703b261f7d0a0739ec807ab"

[[package]]
name = "linux-raw-sys"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6db9c683daf087dc577b7506e9695b3d556a9f3849903fa28186283afd6809e9"

[[package]]
name = "litemap"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23fb14cb19457329c82206317a5663005a4d404783dc74f4252769b0d5f42856"

[[package]]
name = "lock_api"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07af8b9cdd281b7915f413fa73f29ebd5d55d0d3f0155584dade1ff18cea1b17"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13dc2df351e3202783a1fe0d44375f7295ffb4049267b0f3018346dc122a1d94"

[[package]]
name = "lru"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999beba7b6e8345721bd280141ed958096a2e4abdf74f67ff4ce49b4b54e47a"
dependencies = [
 "hashbrown 0.12.3",
]

[[package]]
name = "lru-slab"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "112b39cec0b298b6c1999fee3e31427f74f676e4cb9879ed1a121b43661a4154"

[[package]]
name = "lz4"
version = "1.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a20b523e860d03443e98350ceaac5e71c6ba89aea7d960769ec3ce37f4de5af4"
dependencies = [
 "lz4-sys",
]

[[package]]
name = "lz4-sys"
version = "1.11.1+lz4-1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bd8c0d6c6ed0cd30b3652886bb8711dc4bb01d637a68105a3d5158039b418e6"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "matches"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2532096657941c2fea9c289d370a250971c689d4f143798ff67113ec042024a5"

[[package]]
name = "matchit"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7465ac9959cc2b1404e8e2367b43684a6d13790fe23056cc8c6c5a6b7bcb94"

[[package]]
name = "memchr"
version = "2.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3"

[[package]]
name = "memmap2"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83faa42c0a078c393f6b29d5db232d8be22776a891f8f56e5284faee4a20b327"
dependencies = [
 "libc",
]

[[package]]
name = "memmap2"
version = "0.9.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "483758ad303d734cec05e5c12b41d7e93e6a6390c5e9dae6bdeb7c1259012d28"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "488016bfae457b036d996092f6cb448677611ce4449e970ceaf42695203f218a"
dependencies = [
 "autocfg",
]

[[package]]
name = "merlin"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58c38e2799fc0978b65dfff8023ec7843e2330bb462f19198840b34b6582397d"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "mime"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6877bb514081ee2a7ff5ef9de3281f14a4dd4bceac4c09388074a6b5df8a139a"

[[package]]
name = "min-max-heap"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2687e6cf9c00f48e9284cf9fd15f2ef341d03cc7743abf9df4c5f07fdee50b18"

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e3e04debbb59698c15bacbb6d93584a8c0ca9cc3213cb423d31f760d8843ce5"
dependencies = [
 "adler2",
]

[[package]]
name = "mio"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2886843bf800fba2e3377cff24abf6379b4c4d5c6681eaf9ea5b0d15090450bd"
dependencies = [
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.52.0",
]

[[package]]
name = "mockall"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c84490118f2ee2d74570d114f3d0493cbf02790df303d2707606c3e14e07c96"
dependencies = [
 "cfg-if 1.0.0",
 "downcast",
 "fragile",
 "lazy_static",
 "mockall_derive",
 "predicates 2.1.5",
 "predicates-tree",
]

[[package]]
name = "mockall_derive"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22ce75669015c4f47b289fd4d4f56e894e4c96003ffdf3ac51313126f94c6cbb"
dependencies = [
 "cfg-if 1.0.0",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "modular-bitfield"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a53d79ba8304ac1c4f9eb3b9d281f21f7be9d4626f72ce7df4ad8fbde4f38a74"
dependencies = [
 "modular-bitfield-impl",
 "static_assertions",
]

[[package]]
name = "modular-bitfield-impl"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a7d5f7076603ebc68de2dc6a650ec331a062a13abaa346975be747bbfa4b789"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "multimap"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5ce46fe64a9d73be07dcbe690a38ce1b293be448fd8ce1e6c1b8062c9f72c6a"

[[package]]
name = "native-tls"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87de3442987e9dbec73158d5c715e7ad9072fda936bb03d19d7fa10e00520f0e"
dependencies = [
 "libc",
 "log",
 "openssl",
 "openssl-probe",
 "openssl-sys",
 "schannel",
 "security-framework 2.11.1",
 "security-framework-sys",
 "tempfile",
]

[[package]]
name = "net2"
version = "0.2.39"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b13b648036a2339d06de780866fbdfda0dde886de7b3af2ddeba8b14f4ee34ac"
dependencies = [
 "cfg-if 0.1.10",
 "libc",
 "winapi 0.3.9",
]

[[package]]
name = "nix"
version = "0.30.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74523f3a35e05aba87a1d978330aef40f67b0304ac79c1c00b294c9830543db6"
dependencies = [
 "bitflags 2.9.1",
 "cfg-if 1.0.0",
 "cfg_aliases",
 "libc",
 "memoffset",
]

[[package]]
name = "no-std-compat"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b93853da6d84c2e3c7d730d6473e8817692dd89be387eb01b94d7f108ecb5b8c"

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "nonzero_ext"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38bf9645c8b145698bb0b18a4637dcacbc421ea49bef2317e4fd8065a387cf21"

[[package]]
name = "normalize-line-endings"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61807f77802ff30975e01f4f071c8ba10c022052f98b3294119f3e615d13e5be"

[[package]]
name = "num"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8536030f9fea7127f841b45bb6243b27255787fb4eb83958aa1ef9d2fdc0c36"
dependencies = [
 "num-bigint 0.2.6",
 "num-complex",
 "num-integer",
 "num-iter",
 "num-rational",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "090c7f9998ee0ff65aa5b723e4009f7b217707f1fb5ea551329cc4d6231fb304"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5e44f723f1133c9deac646763579fdb3ac745e418f2a7af9cd0c431da1f20b9"
dependencies = [
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6b19411a9719e753aff12e5187b74d60d3dc449ec3f4dc21e3989c3f554bc95"
dependencies = [
 "autocfg",
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3955f1a9c7c0c15e092f9c887db08b1fc683305fdf6eb6684f22555355e202"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "num-integer"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7969661fd2958a5cb096e56c8e1ad0444ac2bbcd0061bd28660485a44879858f"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1429034a0490724d0075ebb2bc9e875d6503c3cf69e235a8941aa757d83ef5bf"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c000134b5dbf44adc5cb772486d335293351644b801551abe8f75c84cfa4aef"
dependencies = [
 "autocfg",
 "num-bigint 0.2.6",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
]

[[package]]
name = "num_cpus"
version = "1.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91df4bbde75afed763b708b7eee1e8e7651e02d97f6d5dd763e89367e957b23b"
dependencies = [
 "hermit-abi 0.5.2",
 "libc",
]

[[package]]
name = "num_enum"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a973b4e44ce6cad84ce69d797acf9a044532e4184c4f267913d1b546a0727b7a"
dependencies = [
 "num_enum_derive",
 "rustversion",
]

[[package]]
name = "num_enum_derive"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77e878c846a8abae00dd069496dbe8751b16ac1c3d6bd2a7283a938e8228f90d"
dependencies = [
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "number_prefix"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b246a0e5f20af87141b25c173cd1b609bd7779a4617d6ec582abaf90870f3"

[[package]]
name = "object"
version = "0.36.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87"
dependencies = [
 "crc32fast",
 "hashbrown 0.15.2",
 "indexmap 2.10.0",
 "memchr",
]

[[package]]
name = "oid-registry"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bedf36ffb6ba96c2eb7144ef6270557b52e54b20c0a8e1eb2ff99a6c6959bff"
dependencies = [
 "asn1-rs",
]

[[package]]
name = "once_cell"
version = "1.20.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "945462a4b81e43c4e3ba96bd7b49d834c6f61198356aa858733bc4acf3cbe62e"

[[package]]
name = "opaque-debug"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08d65885ee38876c4f86fa503fb49d7b507c2b62552df7c70b2fce627e06381"

[[package]]
name = "openssl"
version = "0.10.72"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fedfea7d58a1f73118430a55da6a286e7b044961736ce96a16a17068ea25e5da"
dependencies = [
 "bitflags 2.9.1",
 "cfg-if 1.0.0",
 "foreign-types",
 "libc",
 "once_cell",
 "openssl-macros",
 "openssl-sys",
]

[[package]]
name = "openssl-macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a948666b637a0f465e8564c73e89d4dde00d72d4d473cc972f390fc3dcee7d9c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "openssl-probe"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d05e27ee213611ffe7d6348b942e8f942b37114c00cc03cec254295a4a17852e"

[[package]]
name = "openssl-src"
version = "300.4.2****.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "168ce4e058f975fe43e89d9ccf78ca668601887ae736090aacc23ae353c298e2"
dependencies = [
 "cc",
]

[[package]]
name = "openssl-sys"
version = "0.9.107"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8288979acd84749c744a9014b4382d42b8f7b2592847b5afb2ed29e5d16ede07"
dependencies = [
 "cc",
 "libc",
 "openssl-src",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "opentelemetry"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6105e89802af13fdf48c49d7646d3b533a70e536d818aae7e78ba0433d01acb8"
dependencies = [
 "async-trait",
 "crossbeam-channel",
 "futures-channel",
 "futures-executor",
 "futures-util",
 "js-sys",
 "lazy_static",
 "percent-encoding 2.3.1",
 "pin-project",
 "rand 0.8.5",
 "thiserror 1.0.69",
]

[[package]]
name = "os_str_bytes"
version = "6.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2355d85b9a3786f481747ced0e0ff2ba35213a1f9bd406ed906554d7af805a1"

[[package]]
name = "parking"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f38d5652c16fde515bb1ecef450ab0f6a219d619a7274976324d5e377f7dceba"

[[package]]
name = "parking_lot"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d17b78036a60663b797adeaee46f5c9dfebb86948d1255007a1d6be0271ff99"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.6",
]

[[package]]
name = "parking_lot"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1bf18183cf54e8d6059647fc3063646a1801cf30896933ec2311622cc4b9a27"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "parking_lot_core"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a2cfe6f0ad2bfc16aefa463b497d5c7a5ecd44a23efa72aa342d90177356dc"
dependencies = [
 "cfg-if 1.0.0",
 "instant",
 "libc",
 "redox_syscall 0.2.16",
 "smallvec",
 "winapi 0.3.9",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if 1.0.0",
 "libc",
 "redox_syscall 0.5.10",
 "smallvec",
 "windows-targets 0.52.6",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pbkdf2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "216eaa586a190f0a738f2f918511eecfa90f13295abec0e457cdebcceda80cbd"
dependencies = [
 "crypto-mac",
]

[[package]]
name = "pbkdf2"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83a0692ec44e4cf1ef28ca317f14f8f07da2d95ec3fa01f86e4467b725e60917"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "pem"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8835c273a76a90455d7344889b0964598e3316e2a79ede8e36f16bdcf2228b8"
dependencies = [
 "base64 0.13.1",
]

[[package]]
name = "percent-encoding"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31010dd2e1ac33d5b46a5b413495239882813e0369f8ed8a5e266f173602f831"

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "percentage"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fd23b938276f14057220b707937bcb42fa76dda7560e57a2da30cb52d557937"
dependencies = [
 "num",
]

[[package]]
name = "pest"
version = "2.7.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b7cafe60d6cf8e62e1b9b2ea516a089c008945bb5a275416789e7db0bc199dc"
dependencies = [
 "memchr",
 "thiserror 2.0.12",
 "ucd-trie",
]

[[package]]
name = "pest_derive"
version = "2.7.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "816518421cfc6887a0d62bf441b6ffb4536fcc926395a69e1a85852d4363f57e"
dependencies = [
 "pest",
 "pest_generator",
]

[[package]]
name = "pest_generator"
version = "2.7.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d1396fd3a870fc7838768d171b4616d5c91f6cc25e377b673d714567d99377b"
dependencies = [
 "pest",
 "pest_meta",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "pest_meta"
version = "2.7.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1e58089ea25d717bfd31fb534e4f3afcc2cc569c70de3e239778991ea3b7dea"
dependencies = [
 "once_cell",
 "pest",
 "sha2 0.10.9",
]

[[package]]
name = "petgraph"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4c5cc86750666a3ed20bdaf5ca2a0344f9c67674cae0515bec2da16fbaa47db"
dependencies = [
 "fixedbitset",
 "indexmap 2.10.0",
]

[[package]]
name = "pin-project"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677f1add503faace112b9f1373e43e9e054bfdd22ff1a63c1bc485eaec6a6a8a"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e918e4ff8c4549eb882f14b3a4bc8c8bc93de829416eacf579f1207a8fbf861"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "pin-project-lite"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b3cff922bd51709b605d9ead9aa71031d81447142d828eb4a6eba76fe619f9b"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "pkg-config"
version = "0.3.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7edddbd0b52d732b21ad9a5fab5c704c14cd949e5e9a1ec5929a24fded1b904c"

[[package]]
name = "polyval"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d1fe60d06143b2430aa532c94cfe9e29783047f06c0d7fd359a9a51b729fa25"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "opaque-debug",
 "universal-hash",
]

[[package]]
name = "portable-atomic"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "350e9b48cbc6b0e028b0473b114454c6316e57336ee184ceab6e53f72c178b3e"

[[package]]
name = "portable-atomic-util"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8a2f0d8d040d7848a709caf78912debcc3f33ee4b3cac47d73d1e1069e83507"
dependencies = [
 "portable-atomic",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "ppv-lite86"
version = "0.2.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77957b295656769bb8ad2b6a6b09d897d94f05c41b069aede1fcdaa675eaea04"
dependencies = [
 "zerocopy",
]

[[package]]
name = "predicates"
version = "2.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59230a63c37f3e18569bdb90e4a89cbf5bf8b06fea0b84e65ea10cc4df47addd"
dependencies = [
 "difflib",
 "float-cmp",
 "itertools 0.10.5",
 "normalize-line-endings",
 "predicates-core",
 "regex",
]

[[package]]
name = "predicates"
version = "3.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5d19ee57562043d37e82899fade9a22ebab7be9cef5026b07fda9cdd4293573"
dependencies = [
 "anstyle",
 "difflib",
 "predicates-core",
]

[[package]]
name = "predicates-core"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "727e462b119fe9c93fd0eb1429a5f7647394014cf3c04ab2c0350eeb09095ffa"

[[package]]
name = "predicates-tree"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72dd2d6d381dfb73a193c7fca536518d7caee39fc8503f74e7dc0be0531b425c"
dependencies = [
 "predicates-core",
 "termtree",
]

[[package]]
name = "pretty-hex"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6fa0831dd7cc608c38a5e323422a0077678fa5744aa2be4ad91c4ece8eec8d5"

[[package]]
name = "prettyplease"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c8646e95016a7a6c4adea95bafa8a16baab64b583356217f2c85db4a39d9a86"
dependencies = [
 "proc-macro2",
 "syn 1.0.109",
]

[[package]]
name = "prio-graph"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f28921629370a46cf564f6ba1828bd8d1c97f7fad4ee9d1c6438f92feed6b8d"
dependencies = [
 "ahash 0.8.11",
]

[[package]]
name = "proc-macro-crate"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6ea3c4595b96363c13943497db34af4460fb474a95c43f4446ad341b8c9785"
dependencies = [
 "toml",
]

[[package]]
name = "proc-macro-crate"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edce586971a4dfaa28950c6f18ed55e0406c1ab88bbce2c6f6293a7aaba73d35"
dependencies = [
 "toml_edit",
]

[[package]]
name = "proc-macro-error-attr2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96de42df36bb9bba5542fe9f1a054b8cc87e172759a1868aa05c1f3acc89dfc5"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "proc-macro-error2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11ec05c52be0a07b08061f7dd003e7d7092e0472bc731b4af7bb1ef876109802"
dependencies = [
 "proc-macro-error-attr2",
 "proc-macro2",
 "quote",
]

[[package]]
name = "proc-macro2"
version = "1.0.94"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a31971752e70b8b2686d7e46ec17fb38dad4051d94024c88df49b667caea9c84"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "proptest"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fcdab19deb5195a31cf7726a210015ff1496ba1464fd42cb4f537b8b01b471f"
dependencies = [
 "bit-set",
 "bit-vec",
 "bitflags 2.9.1",
 "lazy_static",
 "num-traits",
 "rand 0.9.1",
 "rand_chacha 0.9.0",
 "rand_xorshift",
 "regex-syntax",
 "rusty-fork",
 "tempfile",
 "unarray",
]

[[package]]
name = "prost"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b82eaa1d779e9a4bc1c3217db8ffbeabaae1dca241bf70183242128d48681cd"
dependencies = [
 "bytes",
 "prost-derive",
]

[[package]]
name = "prost-build"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "119533552c9a7ffacc21e099c24a0ac8bb19c2a2a3f363de84cd9b844feab270"
dependencies = [
 "bytes",
 "heck 0.4.1",
 "itertools 0.10.5",
 "lazy_static",
 "log",
 "multimap",
 "petgraph",
 "prettyplease",
 "prost",
 "prost-types",
 "regex",
 "syn 1.0.109",
 "tempfile",
 "which",
]

[[package]]
name = "prost-derive"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d2d8d10f3c6ded6da8b05b5fb3b8a5082514344d56c9f871412d29b4e075b4"
dependencies = [
 "anyhow",
 "itertools 0.10.5",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "prost-types"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "213622a1460818959ac1181aaeb2dc9c7f63df720db7d788b3e24eacd1983e13"
dependencies = [
 "prost",
]

[[package]]
name = "protobuf-src"
version = "1.1.0+21.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7ac8852baeb3cc6fb83b93646fb93c0ffe5d14bf138c945ceb4b9948ee0e3c1"
dependencies = [
 "autotools",
]

[[package]]
name = "qstring"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d464fae65fff2680baf48019211ce37aaec0c78e9264c84a3e484717f965104e"
dependencies = [
 "percent-encoding 2.3.1",
]

[[package]]
name = "qualifier_attr"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e2e25ee72f5b24d773cae88422baddefff7714f97aab68d96fe2b6fc4a28fb2"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "quanta"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bd1fe6824cea6538803de3ff1bc0cf3949024db3d43c9643024bfb33a807c0e"
dependencies = [
 "crossbeam-utils",
 "libc",
 "once_cell",
 "raw-cpuid",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "web-sys",
 "winapi 0.3.9",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quinn"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "626214629cda6781b6dc1d316ba307189c85ba657213ce642d9c77670f8202c8"
dependencies = [
 "bytes",
 "cfg_aliases",
 "pin-project-lite",
 "quinn-proto",
 "quinn-udp",
 "rustc-hash 2.1.1",
 "rustls 0.23.29",
 "socket2 0.5.10",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-proto"
version = "0.11.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49df843a9161c85bb8aae55f101bc0bac8bcafd637a620d9122fd7e0b2f7422e"
dependencies = [
 "bytes",
 "fastbloom",
 "getrandom 0.3.1",
 "lru-slab",
 "rand 0.9.1",
 "ring",
 "rustc-hash 2.1.1",
 "rustls 0.23.29",
 "rustls-pki-types",
 "rustls-platform-verifier",
 "slab",
 "thiserror 2.0.12",
 "tinyvec",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-udp"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e46f3055866785f6b92bc6164b76be02ca8f2eb4b002c0354b28cf4c119e5944"
dependencies = [
 "cfg_aliases",
 "libc",
 "once_cell",
 "socket2 0.5.10",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "quote"
version = "1.0.39"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1f1914ce909e1658d9907913b4b91947430c7d9be598b15a1912935b8c04801"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.16",
 "libc",
 "rand_chacha 0.2.2",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
]

[[package]]
name = "rand"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fbfd9d094a40bf3ae768db9361049ace4c0e04a4fd6b359518bd7b73a73dd97"
dependencies = [
 "rand_chacha 0.9.0",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4c8ed856279c9737206bf725bf36935d8666ead7aa69b52be55af369d193402"
dependencies = [
 "ppv-lite86",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3022b5f1df60f26e1ffddd6c66e8aa15de382ae63b3a0c1bfc0e4d3e3f325cb"
dependencies = [
 "ppv-lite86",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.16",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.15",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"
dependencies = [
 "getrandom 0.3.1",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rand_xorshift"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "513962919efc330f829edb2535844d1b912b0fbe2ca165d613e4e8788bb05a5a"
dependencies = [
 "rand_core 0.9.3",
]

[[package]]
name = "rand_xoshiro"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f97cdb2a36ed4183de61b2f824cc45c9f1037f28afe0a322e9fff4c108b5aaa"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "raw-cpuid"
version = "11.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6df7ab838ed27997ba19a4664507e6f82b41fe6e20be42929332156e5e85146"
dependencies = [
 "bitflags 2.9.1",
]

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "redox_syscall"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b8c0c260b63a8219631167be35e6a988e9554dbd323f8bd08439c8ed1302bd1"
dependencies = [
 "bitflags 2.9.1",
]

[[package]]
name = "redox_users"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba009ff324d1fc1b900bd1fdb31564febe58a8ccc8a6fdbb93b543d33b13ca43"
dependencies = [
 "getrandom 0.2.15",
 "libredox",
 "thiserror 1.0.69",
]

[[package]]
name = "reed-solomon-erasure"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7263373d500d4d4f505d43a2a662d475a894aa94503a1ee28e9188b5f3960d4f"
dependencies = [
 "cc",
 "libc",
 "libm",
 "lru",
 "parking_lot 0.11.2",
 "smallvec",
 "spin",
]

[[package]]
name = "ref-cast"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a0ae411dbe946a674d89546582cea4ba2bb8defac896622d6496f14c23ba5cf"
dependencies = [
 "ref-cast-impl",
]

[[package]]
name = "ref-cast-impl"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1165225c21bff1f3bbce98f5a1f889949bc902d3575308cc7b0de30b4f6d27c7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "regex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b544ef1b4eac5dc2db33ea63606ae9ffcfac26c1416a2806ae0bf5f56b201191"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata",
 "regex-syntax",
]

[[package]]
name = "regex-automata"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "809e8dc61f6de73b46c85f4c96486310fe304c434cfa43669d7b40f711150908"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax",
]

[[package]]
name = "regex-syntax"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b15c43186be67a4fd63bee50d0303afffcef381492ebe2c5d87f324e1b8815c"

[[package]]
name = "reqwest"
version = "0.11.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd67538700a17451e7cba03ac727fb961abb7607553461627b97de0b89cf4a62"
dependencies = [
 "base64 0.21.7",
 "bytes",
 "encoding_rs",
 "futures-core",
 "futures-util",
 "h2",
 "http 0.2.12",
 "http-body 0.4.6",
 "hyper 0.14.32",
 "hyper-tls",
 "ipnet",
 "js-sys",
 "log",
 "mime",
 "native-tls",
 "once_cell",
 "percent-encoding 2.3.1",
 "pin-project-lite",
 "rustls-pemfile",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper 0.1.2",
 "system-configuration",
 "tokio",
 "tokio-native-tls",
 "tower-service",
 "url 2.5.4",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "winreg",
]

[[package]]
name = "reqwest"
version = "0.12.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbc931937e6ca3a06e3b6c0aa7841849b160a90351d6ab467a8b9b9959767531"
dependencies = [
 "async-compression",
 "base64 0.22.1",
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-rustls",
 "hyper-util",
 "js-sys",
 "log",
 "percent-encoding 2.3.1",
 "pin-project-lite",
 "quinn",
 "rustls 0.23.29",
 "rustls-pki-types",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper 1.0.2",
 "tokio",
 "tokio-rustls 0.26.2",
 "tokio-util 0.7.15",
 "tower 0.5.2",
 "tower-http",
 "tower-service",
 "url 2.5.4",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "webpki-roots 1.0.1",
]

[[package]]
name = "reqwest-middleware"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57f17d28a6e6acfe1733fe24bcd30774d13bffa4b8a22535b4c8c98423088d4e"
dependencies = [
 "anyhow",
 "async-trait",
 "http 1.3.1",
 "reqwest 0.12.22",
 "serde",
 "thiserror 1.0.69",
 "tower-service",
]

[[package]]
name = "ring"
version = "0.17.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70ac5d832aa16abd7d1def883a8545280c20a60f523a370aa3a9617c2b8550ee"
dependencies = [
 "cc",
 "cfg-if 1.0.0",
 "getrandom 0.2.15",
 "libc",
 "untrusted",
 "windows-sys 0.52.0",
]

[[package]]
name = "rocksdb"
version = "0.23.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26ec73b20525cb235bad420f911473b69f9fe27cc856c5461bccd7e4af037f43"
dependencies = [
 "libc",
 "librocksdb-sys",
]

[[package]]
name = "rolling-file"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8395b4f860856b740f20a296ea2cd4d823e81a2658cf05ef61be22916026a906"
dependencies = [
 "chrono",
]

[[package]]
name = "rpassword"
version = "7.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66d4c8b64f049c6721ec8ccec37ddfc3d641c4a7fca57e8f2a89de509c73df39"
dependencies = [
 "libc",
 "rtoolbox",
 "windows-sys 0.59.0",
]

[[package]]
name = "rtoolbox"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c247d24e63230cdb56463ae328478bd5eac8b8faa8c69461a77e8e323afac90e"
dependencies = [
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "rustc-demangle"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "719b953e2095829ee67db738b3bfa9fa368c94900df327b3f07fe6e794d2fe1f"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"

[[package]]
name = "rustc_version"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfcb3a22ef46e85b45de6ee7e79d063319ebb6594faafcf1c225ea92ab6e9b92"
dependencies = [
 "semver",
]

[[package]]
name = "rusticata-macros"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faf0c4a6ece9950b9abdb62b1cfcf2a68b3b67a10ba445b3bb85be2a293d0632"
dependencies = [
 "nom",
]

[[package]]
name = "rustix"
version = "0.38.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdb5bc1ae2baa591800df16c9ca78619bf65c0488b41b96ccec5d11220d8c154"
dependencies = [
 "bitflags 2.9.1",
 "errno",
 "libc",
 "linux-raw-sys 0.4.15",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustix"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17f8dcd64f141950290e45c99f7710ede1b600297c91818bb30b3667c0f45dc0"
dependencies = [
 "bitflags 2.9.1",
 "errno",
 "libc",
 "linux-raw-sys 0.9.2",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustls"
version = "0.21.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f56a14d1f48b391359b22f731fd4bd7e43c97f3c50eee276f3aa09c94784d3e"
dependencies = [
 "log",
 "ring",
 "rustls-webpki 0.101.7",
 "sct",
]

[[package]]
name = "rustls"
version = "0.23.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2491382039b29b9b11ff08b76ff6c97cf287671dbb74f0be44bda389fffe9bd1"
dependencies = [
 "once_cell",
 "ring",
 "rustls-pki-types",
 "rustls-webpki 0.103.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "rustls-native-certs"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcff2dd52b58a8d98a70243663a0d234c4e2b79235637849d15913394a247d3"
dependencies = [
 "openssl-probe",
 "rustls-pki-types",
 "schannel",
 "security-framework 3.2.0",
]

[[package]]
name = "rustls-pemfile"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c74cae0a4cf6ccbbf5f359f08efdf8ee7e1dc532573bf0db71968cb56b1448c"
dependencies = [
 "base64 0.21.7",
]

[[package]]
name = "rustls-pki-types"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "229a4a4c221013e7e1f1a043678c5cc39fe5171437c88fb47151a21e6f5b5c79"
dependencies = [
 "web-time",
 "zeroize",
]

[[package]]
name = "rustls-platform-verifier"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19787cda76408ec5404443dc8b31795c87cd8fec49762dc75fa727740d34acc1"
dependencies = [
 "core-foundation 0.10.1",
 "core-foundation-sys",
 "jni",
 "log",
 "once_cell",
 "rustls 0.23.29",
 "rustls-native-certs",
 "rustls-platform-verifier-android",
 "rustls-webpki 0.103.4",
 "security-framework 3.2.0",
 "security-framework-sys",
 "webpki-root-certs",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustls-platform-verifier-android"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f87165f0995f63a9fbeea62b64d10b4d9d8e78ec6d7d51fb2125fda7bb36788f"

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring",
 "untrusted",
]

[[package]]
name = "rustls-webpki"
version = "0.103.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a17884ae0c1b773f1ccd2bd4a8c72f16da897310a98b0e84bf349ad5ead92fc"
dependencies = [
 "ring",
 "rustls-pki-types",
 "untrusted",
]

[[package]]
name = "rustversion"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eded382c5f5f786b989652c49544c4877d9f015cc22e145a5ea8ea66c2921cd2"

[[package]]
name = "rusty-fork"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb3dcc6e454c328bb824492db107ab7c0ae8fcffe4ad210136ef014458c1bc4f"
dependencies = [
 "fnv",
 "quick-error",
 "tempfile",
 "wait-timeout",
]

[[package]]
name = "ryu"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28d3b2b1366ec20994f1fd18c3c594f05c5dd4bc44d8bb0c1c632c8d6829481f"

[[package]]
name = "safe_arch"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96b02de82ddbe1b636e6170c21be622223aea188ef2e139be0a5b219ec215323"
dependencies = [
 "bytemuck",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "scc"
version = "2.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea091f6cac2595aa38993f04f4ee692ed43757035c36e67c180b6828356385b1"
dependencies = [
 "sdd",
]

[[package]]
name = "schannel"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f29ebaa345f945cec9fbbc532eb307f0fdad8161f281b6369539c8d84876b3d"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "schemars"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd191f9397d57d581cddd31014772520aa448f65ef991055d7f61582c65165f"
dependencies = [
 "dyn-clone",
 "ref-cast",
 "serde",
 "serde_json",
]

[[package]]
name = "schemars"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1375ba8ef45a6f15d83fa8748f1079428295d403d6ea991d09ab100155fbc06d"
dependencies = [
 "dyn-clone",
 "ref-cast",
 "serde",
 "serde_json",
]

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "sct"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da046153aa2352493d6cb7da4b6e5c0c057d8a1d0a9aa8560baffdd945acd414"
dependencies = [
 "ring",
 "untrusted",
]

[[package]]
name = "sdd"
version = "3.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b07779b9b918cc05650cb30f404d4d7835d26df37c235eded8a6832e2fb82cca"

[[package]]
name = "security-framework"
version = "2.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "897b2245f0b511c87893af39b033e5ca9cce68824c4d7e7630b5a1d339658d02"
dependencies = [
 "bitflags 2.9.1",
 "core-foundation 0.9.4",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271720403f46ca04f7ba6f55d438f8bd878d6b8ca0a1046e8228c4145bcbb316"
dependencies = [
 "bitflags 2.9.1",
 "core-foundation 0.10.1",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49db231d56a190491cb4aeda9527f1ad45345af50b0851622a7adb8c03b01c32"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "semver"
version = "1.0.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56e6fa9c48d24d85fb3de5ad847117517440f6beceb7798af16b4a87d616b8d0"

[[package]]
name = "seqlock"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5c67b6f14ecc5b86c66fa63d76b5092352678545a8a3cdae80aef5128371910"
dependencies = [
 "parking_lot 0.12.3",
]

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde-big-array"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11fc7cc2c76d73e0f27ee52abbd64eec84d46f370c88371120433196934e4b7f"
dependencies = [
 "serde",
]

[[package]]
name = "serde_bytes"
version = "0.11.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8437fd221bde2d4ca316d61b90e337e9e702b3820b87d63caa9ba6c02bd06d96"
dependencies = [
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "serde_json"
version = "1.0.142"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "030fedb782600dcbd6f02d479bf0d817ac3bb40d644745b769d6a96bc3afc5a7"
dependencies = [
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3491c14715ca2294c4d6a88f15e84739788c1d030eed8c110436aafdaa2f3fd"
dependencies = [
 "form_urlencoded",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "3.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2c45cd61fefa9db6f254525d46e392b852e0e61d9a1fd36e5bd183450a556d5"
dependencies = [
 "base64 0.22.1",
 "chrono",
 "hex",
 "indexmap 1.9.3",
 "indexmap 2.10.0",
 "schemars 0.9.0",
 "schemars 1.0.3",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_with_macros",
 "time",
]

[[package]]
name = "serde_with_macros"
version = "3.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de90945e6565ce0d9a25098082ed4ee4002e047cb59892c318d66821e14bb30f"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "serde_yaml"
version = "0.9.34+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a8b1a1a2ebf674015cc02edccce75287f1a0130d394307b36743c2f5d504b47"
dependencies = [
 "indexmap 2.10.0",
 "itoa",
 "ryu",
 "serde",
 "unsafe-libyaml",
]

[[package]]
name = "serial_test"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b258109f244e1d6891bf1053a55d63a5cd4f8f4c30cf9a1280989f80e7a1fa9"
dependencies = [
 "futures 0.3.31",
 "log",
 "once_cell",
 "parking_lot 0.12.3",
 "scc",
 "serial_test_derive",
]

[[package]]
name = "serial_test_derive"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d69265a08751de7844521fd15003ae0a888e035773ba05695c5c759a6f89eef"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "sha-1"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99cd6713db3cf16b6c84e06321e049a9b9f699826e16096d23bbcc44d15d51a6"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha2"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d58a1e1bf39749807d89cf2d98ac2dfa0ff1cb3faa38fbb64dd88ac8013d800"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "sha2"
version = "0.10.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7507d819769d01a365ab707794a4084392c824f54a7a6a7862f8c3d0892b283"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha3"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75872d278a8f37ef87fa0ddbda7802605cb18344497949862c0d4dcb291eba60"
dependencies = [
 "digest 0.10.7",
 "keccak",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shell-words"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24188a676b6ae68c3b2cb3a01be17fbf7240ce009799bb56d5b1409051e78fde"

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "signal-hook"
version = "0.3.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d881a16cf4426aa584979d30bd82cb33429027e42122b169753d6ef1085ed6e2"
dependencies = [
 "libc",
 "signal-hook-registry",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9e9e0b4211b72e7b8b6e85c807d36c212bdb33ea8587f7569562a84df5465b1"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "1.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74233d3b3b2f6d4b006dc19dee745e73e2a6bfb6f93607cd3b02bd5b00797d7c"

[[package]]
name = "simpl"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a30f10c911c0355f80f1c2faa8096efc4a58cdf8590b954d5b395efa071c711"

[[package]]
name = "siphasher"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38b58827f4464d87d377d175e90bf58eb00fd8716ff0a62f80356b5e61555d0d"

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"

[[package]]
name = "sized-chunks"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16d69225bde7a69b235da73377861095455d298f2b970996eec25ddbb42b3d1e"
dependencies = [
 "bitmaps",
 "typenum",
]

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "smallvec"
version = "1.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67b1b7a3b5fe4f1376887184045fcf45c69e92af734b7aaddc05fb777b6fbd03"

[[package]]
name = "smpl_jwt"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95b6ff8c21c74ce7744643a7cddbb02579a44f1f77e4316bff1ddb741aca8ac9"
dependencies = [
 "base64 0.13.1",
 "log",
 "openssl",
 "serde",
 "serde_derive",
 "serde_json",
 "simpl",
 "time",
]

[[package]]
name = "socket2"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e22376abed350d73dd1cd119b57ffccad95b4e585a7cda43e286245ce23c0678"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "socket2"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "233504af464074f9d066d7b5416c5f9b894a5862a6506e306f7b816cdd6f1807"
dependencies = [
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "soketto"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41d1c5305e39e09653383c2c7244f2f78b3bcae37cf50c64cb4789c9f5096ec2"
dependencies = [
 "base64 0.13.1",
 "bytes",
 "futures 0.3.31",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha-1",
]

[[package]]
name = "solana-account"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f949fe4edaeaea78c844023bfc1c898e0b1f5a100f8a8d2d0f85d0a7b090258"
dependencies = [
 "bincode",
 "serde",
 "serde_bytes",
 "serde_derive",
 "solana-account-info 2.3.0",
 "solana-clock",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-sysvar",
]

[[package]]
name = "solana-account-decoder"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c212bf3e322fc62958d27e85e69ac035510500d1ddf97ecf2c266e63ce0e528"
dependencies = [
 "Inflector",
 "base64 0.22.1",
 "bincode",
 "bs58",
 "bv",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account",
 "solana-account-decoder-client-types",
 "solana-address-lookup-table-interface",
 "solana-clock",
 "solana-config-program-client",
 "solana-epoch-schedule",
 "solana-fee-calculator",
 "solana-instruction 2.3.0",
 "solana-loader-v3-interface",
 "solana-nonce",
 "solana-program-option 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-slot-hashes",
 "solana-slot-history",
 "solana-stake-interface",
 "solana-sysvar",
 "solana-vote-interface",
 "spl-generic-token",
 "spl-token",
 "spl-token-2022 8.0.1",
 "spl-token-group-interface 0.6.0",
 "spl-token-metadata-interface 0.7.0",
 "thiserror 2.0.12",
 "zstd",
]

[[package]]
name = "solana-account-decoder-client-types"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1792f77a96494c850cd124800fb271c705abe4835dc8c5d586d5e68870ad27d2"
dependencies = [
 "base64 0.22.1",
 "bs58",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account",
 "solana-pubkey 2.4.0",
 "zstd",
]

[[package]]
name = "solana-account-info"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8f5152a288ef1912300fc6efa6c2d1f9bb55d9398eb6c72326360b8063987da"
dependencies = [
 "bincode",
 "serde",
 "solana-program-error 2.2.1",
 "solana-program-memory 2.3.1",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-account-info"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82f4691b69b172c687d218dd2f1f23fc7ea5e9aa79df9ac26dab3d8dd829ce48"
dependencies = [
 "solana-program-error 3.0.0",
 "solana-program-memory 3.0.0",
 "solana-pubkey 3.0.0",
]

[[package]]
name = "solana-accounts-db"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b91b21cfcd8654e561196d737c6396f9719438126684e91b856f301219f3f08c"
dependencies = [
 "agave-io-uring",
 "ahash 0.8.11",
 "bincode",
 "blake3",
 "bv",
 "bytemuck",
 "bytemuck_derive",
 "bzip2",
 "crossbeam-channel",
 "dashmap",
 "indexmap 2.10.0",
 "io-uring",
 "itertools 0.12.1",
 "log",
 "lz4",
 "memmap2 0.9.7",
 "modular-bitfield",
 "num_cpus",
 "num_enum",
 "rand 0.8.5",
 "rayon",
 "seqlock",
 "serde",
 "serde_derive",
 "slab",
 "smallvec",
 "solana-account",
 "solana-address-lookup-table-interface",
 "solana-bucket-map",
 "solana-clock",
 "solana-epoch-schedule",
 "solana-fee-calculator",
 "solana-genesis-config",
 "solana-hash 2.3.0",
 "solana-lattice-hash",
 "solana-measure",
 "solana-message",
 "solana-metrics",
 "solana-nohash-hasher",
 "solana-pubkey 2.4.0",
 "solana-rayon-threadlimit",
 "solana-rent-collector",
 "solana-reward-info",
 "solana-sha256-hasher 2.2.1",
 "solana-slot-hashes",
 "solana-svm-transaction",
 "solana-system-interface",
 "solana-sysvar",
 "solana-time-utils",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "spl-generic-token",
 "static_assertions",
 "tar",
 "tempfile",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-address"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a7a457086457ea9db9a5199d719dc8734dc2d0342fad0d8f77633c31eb62f19"
dependencies = [
 "borsh 1.5.7",
 "bytemuck",
 "bytemuck_derive",
 "curve25519-dalek 4.1.3",
 "five8",
 "five8_const",
 "serde",
 "serde_derive",
 "solana-atomic-u64 3.0.0",
 "solana-define-syscall 3.0.0",
 "solana-program-error 3.0.0",
 "solana-sanitize 3.0.0",
 "solana-sha256-hasher 3.0.0",
]

[[package]]
name = "solana-address-lookup-table-interface"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1673f67efe870b64a65cb39e6194be5b26527691ce5922909939961a6e6b395"
dependencies = [
 "bincode",
 "bytemuck",
 "serde",
 "serde_derive",
 "solana-clock",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-slot-hashes",
]

[[package]]
name = "solana-atomic-u64"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52e52720efe60465b052b9e7445a01c17550666beec855cce66f44766697bc2"
dependencies = [
 "parking_lot 0.12.3",
]

[[package]]
name = "solana-atomic-u64"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a933ff1e50aff72d02173cfcd7511bd8540b027ee720b75f353f594f834216d0"
dependencies = [
 "parking_lot 0.12.3",
]

[[package]]
name = "solana-banks-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70bdbf1c4bd667bae0cbb0ba2cbfd809ac89838e697215a6d21b4ee866aa0143"
dependencies = [
 "borsh 1.5.7",
 "futures 0.3.31",
 "solana-account",
 "solana-banks-interface",
 "solana-clock",
 "solana-commitment-config",
 "solana-hash 2.3.0",
 "solana-message",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-signature 2.3.0",
 "solana-sysvar",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "tarpc",
 "thiserror 2.0.12",
 "tokio",
 "tokio-serde",
]

[[package]]
name = "solana-banks-interface"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f92736b0f47f43386f50e168d229935d5e1dd0b4e1d49be468f0ca3d2d52df6d"
dependencies = [
 "serde",
 "serde_derive",
 "solana-account",
 "solana-clock",
 "solana-commitment-config",
 "solana-hash 2.3.0",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-signature 2.3.0",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "tarpc",
]

[[package]]
name = "solana-banks-server"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cd467bc04b69e703e26b9e93f20653d19ccb81ff014fcdb69c12a69aee19833"
dependencies = [
 "agave-feature-set",
 "bincode",
 "crossbeam-channel",
 "futures 0.3.31",
 "solana-account",
 "solana-banks-interface",
 "solana-client",
 "solana-clock",
 "solana-commitment-config",
 "solana-hash 2.3.0",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-runtime",
 "solana-runtime-transaction",
 "solana-send-transaction-service",
 "solana-signature 2.3.0",
 "solana-svm",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "tarpc",
 "tokio",
 "tokio-serde",
]

[[package]]
name = "solana-big-mod-exp"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75db7f2bbac3e62cfd139065d15bcda9e2428883ba61fc8d27ccb251081e7567"
dependencies = [
 "num-bigint 0.4.6",
 "num-traits",
 "solana-define-syscall 2.3.0",
]

[[package]]
name = "solana-bincode"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19a3787b8cf9c9fe3dd360800e8b70982b9e5a8af9e11c354b6665dd4a003adc"
dependencies = [
 "bincode",
 "serde",
 "solana-instruction 2.3.0",
]

[[package]]
name = "solana-blake3-hasher"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1a0801e25a1b31a14494fc80882a036be0ffd290efc4c2d640bfcca120a4672"
dependencies = [
 "blake3",
 "solana-define-syscall 2.3.0",
 "solana-hash 2.3.0",
 "solana-sanitize 2.2.1",
]

[[package]]
name = "solana-bloom"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f567a371909f669cde322649da30b5cf388690b3da5a13b958858cb431217b0"
dependencies = [
 "bv",
 "fnv",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "solana-sanitize 2.2.1",
 "solana-time-utils",
]

[[package]]
name = "solana-bn254"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4420f125118732833f36facf96a27e7b78314b2d642ba07fa9ffdacd8d79e243"
dependencies = [
 "ark-bn254",
 "ark-ec",
 "ark-ff",
 "ark-serialize",
 "bytemuck",
 "solana-define-syscall 2.3.0",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-borsh"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "718333bcd0a1a7aed6655aa66bef8d7fb047944922b2d3a18f49cbc13e73d004"
dependencies = [
 "borsh 0.10.4",
 "borsh 1.5.7",
]

[[package]]
name = "solana-borsh"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc402b16657abbfa9991cd5cbfac5a11d809f7e7d28d3bb291baeb088b39060e"
dependencies = [
 "borsh 1.5.7",
]

[[package]]
name = "solana-bpf-loader-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a33b37dd45d3e9cadb29e748d83b5eeaa322df59b14645787a55efe27e6b2a14"
dependencies = [
 "bincode",
 "libsecp256k1",
 "num-traits",
 "qualifier_attr",
 "scopeguard",
 "solana-account",
 "solana-account-info 2.3.0",
 "solana-big-mod-exp",
 "solana-bincode",
 "solana-blake3-hasher",
 "solana-bn254",
 "solana-clock",
 "solana-cpi",
 "solana-curve25519",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keccak-hasher",
 "solana-loader-v3-interface",
 "solana-loader-v4-interface",
 "solana-log-collector",
 "solana-measure",
 "solana-packet",
 "solana-poseidon",
 "solana-program-entrypoint",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-sbpf",
 "solana-sdk-ids 2.2.1",
 "solana-secp256k1-recover",
 "solana-sha256-hasher 2.2.1",
 "solana-stable-layout",
 "solana-svm-feature-set",
 "solana-system-interface",
 "solana-sysvar",
 "solana-sysvar-id 2.2.1",
 "solana-timings",
 "solana-transaction-context",
 "solana-type-overrides",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-bucket-map"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31dd17b809ceaff8a847a82fe2149a4509a7072e30757a5813d526fd46fe760c"
dependencies = [
 "bv",
 "bytemuck",
 "bytemuck_derive",
 "memmap2 0.9.7",
 "modular-bitfield",
 "num_enum",
 "rand 0.8.5",
 "solana-clock",
 "solana-measure",
 "solana-pubkey 2.4.0",
 "tempfile",
]

[[package]]
name = "solana-builtins"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72254e1c55b25fa5a58af23fb7e4740ca757a293c898858b4a48bd2fa8042d84"
dependencies = [
 "agave-feature-set",
 "solana-bpf-loader-program",
 "solana-compute-budget-program",
 "solana-hash 2.3.0",
 "solana-loader-v4-program",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-stake-program",
 "solana-system-program",
 "solana-vote-program",
 "solana-zk-elgamal-proof-program",
 "solana-zk-token-proof-program",
]

[[package]]
name = "solana-builtins-default-costs"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d06100155db23ed947f105aa63d46458faa4a58e971b628c4e786509da6bbcd"
dependencies = [
 "agave-feature-set",
 "ahash 0.8.11",
 "log",
 "solana-bpf-loader-program",
 "solana-compute-budget-program",
 "solana-loader-v4-program",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-stake-program",
 "solana-system-program",
 "solana-vote-program",
]

[[package]]
name = "solana-clap-utils"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8068341b24e766677ac169b9b4402cab7de8ce61d200792897f0b283f0c42d70"
dependencies = [
 "chrono",
 "clap 2.34.0",
 "rpassword",
 "solana-clock",
 "solana-cluster-type",
 "solana-commitment-config",
 "solana-derivation-path 2.2.1",
 "solana-hash 2.3.0",
 "solana-keypair",
 "solana-message",
 "solana-native-token",
 "solana-presigner",
 "solana-pubkey 2.4.0",
 "solana-remote-wallet",
 "solana-seed-phrase 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "thiserror 2.0.12",
 "tiny-bip39",
 "uriparse",
 "url 2.5.4",
]

[[package]]
name = "solana-clap-v3-utils"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4db6a8a95cb7ced34573921ac26d2f7845eb8ac0b7237247ab8794f68869655"
dependencies = [
 "chrono",
 "clap 3.2.25",
 "rpassword",
 "solana-clock",
 "solana-cluster-type",
 "solana-commitment-config",
 "solana-derivation-path 2.2.1",
 "solana-hash 2.3.0",
 "solana-keypair",
 "solana-message",
 "solana-native-token",
 "solana-presigner",
 "solana-pubkey 2.4.0",
 "solana-remote-wallet",
 "solana-seed-derivable 2.2.1",
 "solana-seed-phrase 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-zk-token-sdk",
 "thiserror 2.0.12",
 "tiny-bip39",
 "uriparse",
 "url 2.5.4",
]

[[package]]
name = "solana-cli-config"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "699bfa7683c474146a90b18be7bc44ae466e0381a01361bec17e6acb95cc29be"
dependencies = [
 "dirs-next",
 "serde",
 "serde_derive",
 "serde_yaml",
 "solana-clap-utils",
 "solana-commitment-config",
 "url 2.5.4",
]

[[package]]
name = "solana-cli-output"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "471d71e21d187301a6b2506e952f46b538885cee4d0204b3f9adda183f310935"
dependencies = [
 "Inflector",
 "agave-reserved-account-keys",
 "base64 0.22.1",
 "chrono",
 "clap 2.34.0",
 "console 0.15.11",
 "humantime",
 "indicatif",
 "pretty-hex",
 "semver",
 "serde",
 "serde_json",
 "solana-account",
 "solana-account-decoder",
 "solana-bincode",
 "solana-clap-utils",
 "solana-cli-config",
 "solana-clock",
 "solana-epoch-info",
 "solana-hash 2.3.0",
 "solana-message",
 "solana-native-token",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-rpc-client-api",
 "solana-sdk-ids 2.2.1",
 "solana-signature 2.3.0",
 "solana-stake-interface",
 "solana-system-interface",
 "solana-sysvar",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status",
 "solana-vote-program",
 "spl-memo",
]

[[package]]
name = "solana-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a13f3570a0639081ce8fc5d3920b093f807c5589d053f74436a6bc6407241d3"
dependencies = [
 "async-trait",
 "bincode",
 "dashmap",
 "futures 0.3.31",
 "futures-util",
 "indexmap 2.10.0",
 "indicatif",
 "log",
 "quinn",
 "rayon",
 "solana-account",
 "solana-client-traits",
 "solana-commitment-config",
 "solana-connection-cache",
 "solana-epoch-info",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-measure",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-pubsub-client",
 "solana-quic-client",
 "solana-quic-definitions",
 "solana-rpc-client",
 "solana-rpc-client-api",
 "solana-rpc-client-nonce-utils",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-streamer",
 "solana-thin-client",
 "solana-time-utils",
 "solana-tpu-client",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-udp-client",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-client-traits"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83f0071874e629f29e0eb3dab8a863e98502ac7aba55b7e0df1803fc5cac72a7"
dependencies = [
 "solana-account",
 "solana-commitment-config",
 "solana-epoch-info",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-system-interface",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
]

[[package]]
name = "solana-clock"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bb482ab70fced82ad3d7d3d87be33d466a3498eb8aa856434ff3c0dfc2e2e31"
dependencies = [
 "serde",
 "serde_derive",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-cluster-type"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ace9fea2daa28354d107ea879cff107181d85cd4e0f78a2bedb10e1a428c97e"
dependencies = [
 "serde",
 "serde_derive",
 "solana-hash 2.3.0",
]

[[package]]
name = "solana-commitment-config"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac49c4dde3edfa832de1697e9bcdb7c3b3f7cb7a1981b7c62526c8bb6700fb73"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "solana-compute-budget"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "920340599f6e67fe6a49188609105edf983195787489265c98ff50b41d6ce1b4"
dependencies = [
 "solana-fee-structure",
 "solana-program-runtime",
]

[[package]]
name = "solana-compute-budget-instruction"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8be5c9ffd6dd67004bc93dfd2f613ccb01b95fd4e0ad037434558cfa0fe130a7"
dependencies = [
 "agave-feature-set",
 "log",
 "solana-borsh 2.2.1",
 "solana-builtins-default-costs",
 "solana-compute-budget",
 "solana-compute-budget-interface",
 "solana-instruction 2.3.0",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-svm-transaction",
 "solana-transaction-error 2.2.1",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-compute-budget-interface"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8432d2c4c22d0499aa06d62e4f7e333f81777b3d7c96050ae9e5cb71a8c3aee4"
dependencies = [
 "borsh 1.5.7",
 "serde",
 "serde_derive",
 "solana-instruction 2.3.0",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-compute-budget-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cdc0130c54e2b2acc3b943d4a1a789fb48c9f72af5c61f5dde393e1e50223013"
dependencies = [
 "solana-program-runtime",
]

[[package]]
name = "solana-config-program-client"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53aceac36f105fd4922e29b4f0c1f785b69d7b3e7e387e384b8985c8e0c3595e"
dependencies = [
 "bincode",
 "borsh 0.10.4",
 "kaigan",
 "serde",
 "solana-program",
]

[[package]]
name = "solana-connection-cache"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a03d5dfebc114ca69f283cb0304bc8ae06ea727f1d1e1f2c5dbdb95c5dc7448"
dependencies = [
 "async-trait",
 "bincode",
 "crossbeam-channel",
 "futures-util",
 "indexmap 2.10.0",
 "log",
 "rand 0.8.5",
 "rayon",
 "solana-keypair",
 "solana-measure",
 "solana-metrics",
 "solana-time-utils",
 "solana-transaction-error 2.2.1",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-core"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaf1210d12c5a49cbc9c0e99cbb129d9428d55ad9e247d4bfd10b1bd9c176d4f"
dependencies = [
 "agave-banking-stage-ingress-types",
 "agave-feature-set",
 "agave-transaction-view",
 "ahash 0.8.11",
 "anyhow",
 "arrayvec",
 "assert_matches",
 "async-trait",
 "base64 0.22.1",
 "bincode",
 "bs58",
 "bytes",
 "chrono",
 "conditional-mod",
 "crossbeam-channel",
 "dashmap",
 "derive_more 1.0.0",
 "etcd-client",
 "futures 0.3.31",
 "histogram",
 "itertools 0.12.1",
 "log",
 "lru",
 "min-max-heap",
 "num_enum",
 "prio-graph",
 "qualifier_attr",
 "quinn",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rayon",
 "rolling-file",
 "rustls 0.23.29",
 "serde",
 "serde_bytes",
 "serde_derive",
 "slab",
 "solana-account",
 "solana-accounts-db",
 "solana-address-lookup-table-interface",
 "solana-bincode",
 "solana-bloom",
 "solana-builtins-default-costs",
 "solana-client",
 "solana-clock",
 "solana-compute-budget",
 "solana-compute-budget-instruction",
 "solana-compute-budget-interface",
 "solana-connection-cache",
 "solana-cost-model",
 "solana-entry",
 "solana-epoch-schedule",
 "solana-fee",
 "solana-fee-calculator",
 "solana-fee-structure",
 "solana-genesis-config",
 "solana-geyser-plugin-manager",
 "solana-gossip",
 "solana-hard-forks",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-ledger",
 "solana-loader-v3-interface",
 "solana-measure",
 "solana-message",
 "solana-metrics",
 "solana-native-token",
 "solana-net-utils",
 "solana-nonce",
 "solana-nonce-account",
 "solana-packet",
 "solana-perf",
 "solana-poh",
 "solana-poh-config",
 "solana-pubkey 2.4.0",
 "solana-quic-client",
 "solana-quic-definitions",
 "solana-rayon-threadlimit",
 "solana-rent",
 "solana-rpc",
 "solana-rpc-client-api",
 "solana-runtime",
 "solana-runtime-transaction",
 "solana-sanitize 2.2.1",
 "solana-sdk-ids 2.2.1",
 "solana-send-transaction-service",
 "solana-sha256-hasher 2.2.1",
 "solana-short-vec",
 "solana-shred-version",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-slot-hashes",
 "solana-slot-history",
 "solana-streamer",
 "solana-svm",
 "solana-svm-transaction",
 "solana-system-interface",
 "solana-system-transaction",
 "solana-sysvar",
 "solana-time-utils",
 "solana-timings",
 "solana-tls-utils",
 "solana-tpu-client",
 "solana-tpu-client-next",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status",
 "solana-turbine",
 "solana-unified-scheduler-pool",
 "solana-validator-exit",
 "solana-version",
 "solana-vote",
 "solana-vote-program",
 "solana-wen-restart",
 "static_assertions",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "sys-info",
 "sysctl",
 "tempfile",
 "thiserror 2.0.12",
 "tikv-jemallocator",
 "tokio",
 "tokio-util 0.7.15",
 "trees",
]

[[package]]
name = "solana-cost-model"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dda68d4f7efc466be40596287a34a16854afb6ea4e2ca1cd67a06ec40d09872"
dependencies = [
 "agave-feature-set",
 "ahash 0.8.11",
 "log",
 "solana-bincode",
 "solana-borsh 2.2.1",
 "solana-builtins-default-costs",
 "solana-clock",
 "solana-compute-budget",
 "solana-compute-budget-instruction",
 "solana-compute-budget-interface",
 "solana-fee-structure",
 "solana-metrics",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-runtime-transaction",
 "solana-sdk-ids 2.2.1",
 "solana-svm-transaction",
 "solana-system-interface",
 "solana-transaction-error 2.2.1",
 "solana-vote-program",
]

[[package]]
name = "solana-cpi"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8dc71126edddc2ba014622fc32d0f5e2e78ec6c5a1e0eb511b85618c09e9ea11"
dependencies = [
 "solana-account-info 2.3.0",
 "solana-define-syscall 2.3.0",
 "solana-instruction 2.3.0",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-stable-layout",
]

[[package]]
name = "solana-curve25519"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be64f4005f30cb8de8850a0e03356521da7e35b8c06d85bc79d78f9a74df028a"
dependencies = [
 "bytemuck",
 "bytemuck_derive",
 "curve25519-dalek 4.1.3",
 "solana-define-syscall 2.3.0",
 "subtle",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-decode-error"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10a6a6383af236708048f8bd8d03db8ca4ff7baf4a48e5d580f4cce545925470"
dependencies = [
 "num-traits",
]

[[package]]
name = "solana-define-syscall"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ae3e2abcf541c8122eafe9a625d4d194b4023c20adde1e251f94e056bb1aee2"

[[package]]
name = "solana-define-syscall"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9697086a4e102d28a156b8d6b521730335d6951bd39a5e766512bbe09007cee"

[[package]]
name = "solana-derivation-path"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "939756d798b25c5ec3cca10e06212bdca3b1443cb9bb740a38124f58b258737b"
dependencies = [
 "derivation-path",
 "qstring",
 "uriparse",
]

[[package]]
name = "solana-derivation-path"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff71743072690fdbdfcdc37700ae1cb77485aaad49019473a81aee099b1e0b8c"
dependencies = [
 "derivation-path",
 "qstring",
 "uriparse",
]

[[package]]
name = "solana-ed25519-program"
version = "2.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1feafa1691ea3ae588f99056f4bdd1293212c7ece28243d7da257c443e84753"
dependencies = [
 "bytemuck",
 "bytemuck_derive",
 "ed25519-dalek",
 "solana-feature-set",
 "solana-instruction 2.3.0",
 "solana-precompile-error",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-entry"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc29231440db248d197bc50c9b19d743a66f5ba46f0508708bff5b2de049d72a"
dependencies = [
 "bincode",
 "crossbeam-channel",
 "dlopen2",
 "log",
 "rand 0.8.5",
 "rayon",
 "serde",
 "solana-hash 2.3.0",
 "solana-measure",
 "solana-merkle-tree",
 "solana-metrics",
 "solana-packet",
 "solana-perf",
 "solana-rayon-threadlimit",
 "solana-runtime-transaction",
 "solana-sha256-hasher 2.2.1",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
]

[[package]]
name = "solana-epoch-info"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90ef6f0b449290b0b9f32973eefd95af35b01c5c0c34c569f936c34c5b20d77b"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "solana-epoch-rewards"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b575d3dd323b9ea10bb6fe89bf6bf93e249b215ba8ed7f68f1a3633f384db7"
dependencies = [
 "serde",
 "serde_derive",
 "solana-hash 2.3.0",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-epoch-rewards-hasher"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96c5fd2662ae7574810904585fd443545ed2b568dbd304b25a31e79ccc76e81b"
dependencies = [
 "siphasher 0.3.11",
 "solana-hash 2.3.0",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-epoch-schedule"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fce071fbddecc55d727b1d7ed16a629afe4f6e4c217bc8d00af3b785f6f67ed"
dependencies = [
 "serde",
 "serde_derive",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-example-mocks"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84461d56cbb8bb8d539347151e0525b53910102e4bced875d49d5139708e39d3"
dependencies = [
 "serde",
 "serde_derive",
 "solana-address-lookup-table-interface",
 "solana-clock",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keccak-hasher",
 "solana-message",
 "solana-nonce",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-system-interface",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-faucet"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d1fdac8a04ac59537c62d2829da7edac5eae12c0e81237134fb5931af26e185"
dependencies = [
 "bincode",
 "clap 2.34.0",
 "crossbeam-channel",
 "log",
 "serde",
 "serde_derive",
 "solana-clap-utils",
 "solana-cli-config",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-logger 2.3.1",
 "solana-message",
 "solana-metrics",
 "solana-native-token",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-signer 2.2.1",
 "solana-system-interface",
 "solana-system-transaction",
 "solana-transaction",
 "solana-version",
 "spl-memo",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-feature-gate-interface"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43f5c5382b449e8e4e3016fb05e418c53d57782d8b5c30aa372fc265654b956d"
dependencies = [
 "bincode",
 "serde",
 "serde_derive",
 "solana-account",
 "solana-account-info 2.3.0",
 "solana-instruction 2.3.0",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-system-interface",
]

[[package]]
name = "solana-feature-set"
version = "2.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93b93971e289d6425f88e6e3cb6668c4b05df78b3c518c249be55ced8efd6b6d"
dependencies = [
 "ahash 0.8.11",
 "lazy_static",
 "solana-epoch-schedule",
 "solana-hash 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sha256-hasher 2.2.1",
]

[[package]]
name = "solana-fee"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e71d093270ecbeba22b88e4556c0c02705305c6ed1469d7a31f47f41e7efd827"
dependencies = [
 "agave-feature-set",
 "solana-fee-structure",
 "solana-svm-transaction",
]

[[package]]
name = "solana-fee-calculator"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d89bc408da0fb3812bc3008189d148b4d3e08252c79ad810b245482a3f70cd8d"
dependencies = [
 "log",
 "serde",
 "serde_derive",
]

[[package]]
name = "solana-fee-structure"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33adf673581c38e810bf618f745bf31b683a0a4a4377682e6aaac5d9a058dd4e"
dependencies = [
 "serde",
 "serde_derive",
 "solana-message",
 "solana-native-token",
]

[[package]]
name = "solana-genesis-config"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "968dabd2b92d57131473eddbd475339da530e14f54397386abf303de3a2595a2"
dependencies = [
 "bincode",
 "chrono",
 "memmap2 0.5.10",
 "serde",
 "serde_derive",
 "solana-account",
 "solana-clock",
 "solana-cluster-type",
 "solana-epoch-schedule",
 "solana-fee-calculator",
 "solana-hash 2.3.0",
 "solana-inflation",
 "solana-keypair",
 "solana-logger 2.3.1",
 "solana-native-token",
 "solana-poh-config",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-sha256-hasher 2.2.1",
 "solana-shred-version",
 "solana-signer 2.2.1",
 "solana-time-utils",
]

[[package]]
name = "solana-geyser-plugin-manager"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30cff15ec13620188559cd3c581cdabd9ee291ad8117d2cab11a9c27c7ca25cb"
dependencies = [
 "agave-geyser-plugin-interface",
 "bs58",
 "crossbeam-channel",
 "json5",
 "jsonrpc-core",
 "libloading",
 "log",
 "serde_json",
 "solana-account",
 "solana-accounts-db",
 "solana-clock",
 "solana-entry",
 "solana-ledger",
 "solana-measure",
 "solana-metrics",
 "solana-pubkey 2.4.0",
 "solana-rpc",
 "solana-runtime",
 "solana-signature 2.3.0",
 "solana-transaction",
 "solana-transaction-status",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-gossip"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23348a05bea638fd940d796f701fd994d71a52d77de10c767512544486fb93ad"
dependencies = [
 "agave-feature-set",
 "arrayvec",
 "assert_matches",
 "bincode",
 "bv",
 "clap 2.34.0",
 "crossbeam-channel",
 "flate2",
 "indexmap 2.10.0",
 "itertools 0.12.1",
 "log",
 "lru",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rayon",
 "serde",
 "serde-big-array",
 "serde_bytes",
 "serde_derive",
 "siphasher 1.0.1",
 "solana-bloom",
 "solana-clap-utils",
 "solana-client",
 "solana-clock",
 "solana-connection-cache",
 "solana-entry",
 "solana-epoch-schedule",
 "solana-hash 2.3.0",
 "solana-keypair",
 "solana-ledger",
 "solana-logger 2.3.1",
 "solana-measure",
 "solana-metrics",
 "solana-native-token",
 "solana-net-utils",
 "solana-packet",
 "solana-perf",
 "solana-pubkey 2.4.0",
 "solana-quic-definitions",
 "solana-rayon-threadlimit",
 "solana-rpc-client",
 "solana-runtime",
 "solana-sanitize 2.2.1",
 "solana-serde-varint",
 "solana-sha256-hasher 2.2.1",
 "solana-short-vec",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-streamer",
 "solana-time-utils",
 "solana-tpu-client",
 "solana-transaction",
 "solana-version",
 "solana-vote",
 "solana-vote-program",
 "static_assertions",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-hard-forks"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c28371f878e2ead55611d8ba1b5fb879847156d04edea13693700ad1a28baf"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "solana-hash"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5b96e9f0300fa287b545613f007dfe20043d7812bee255f418c1eb649c93b63"
dependencies = [
 "borsh 1.5.7",
 "bytemuck",
 "bytemuck_derive",
 "five8",
 "js-sys",
 "serde",
 "serde_derive",
 "solana-atomic-u64 2.2.1",
 "solana-sanitize 2.2.1",
 "wasm-bindgen",
]

[[package]]
name = "solana-hash"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a063723b9e84c14d8c0d2cdf0268207dc7adecf546e31251f9e07c7b00b566c"
dependencies = [
 "five8",
 "solana-atomic-u64 3.0.0",
 "solana-sanitize 3.0.0",
]

[[package]]
name = "solana-inflation"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23eef6a09eb8e568ce6839573e4966850e85e9ce71e6ae1a6c930c1c43947de3"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "solana-instruction"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47298e2ce82876b64f71e9d13a46bc4b9056194e7f9937ad3084385befa50885"
dependencies = [
 "bincode",
 "borsh 1.5.7",
 "getrandom 0.2.15",
 "js-sys",
 "num-traits",
 "serde",
 "serde_derive",
 "solana-define-syscall 2.3.0",
 "solana-pubkey 2.4.0",
 "wasm-bindgen",
]

[[package]]
name = "solana-instruction"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8df4e8fcba01d7efa647ed20a081c234475df5e11a93acb4393cc2c9a7b99bab"
dependencies = [
 "solana-define-syscall 3.0.0",
 "solana-instruction-error",
 "solana-pubkey 3.0.0",
]

[[package]]
name = "solana-instruction-error"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f0d483b8ae387178d9210e0575b666b05cdd4bd0f2f188128249f6e454d39d"
dependencies = [
 "num-traits",
 "solana-program-error 3.0.0",
]

[[package]]
name = "solana-instructions-sysvar"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0e85a6fad5c2d0c4f5b91d34b8ca47118fc593af706e523cdbedf846a954f57"
dependencies = [
 "bitflags 2.9.1",
 "solana-account-info 2.3.0",
 "solana-instruction 2.3.0",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-sanitize 2.2.1",
 "solana-sdk-ids 2.2.1",
 "solana-serialize-utils 2.2.1",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-instructions-sysvar"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ddf67876c541aa1e21ee1acae35c95c6fbc61119814bfef70579317a5e26955"
dependencies = [
 "bitflags 2.9.1",
 "solana-account-info 3.0.0",
 "solana-instruction 3.0.0",
 "solana-instruction-error",
 "solana-program-error 3.0.0",
 "solana-pubkey 3.0.0",
 "solana-sanitize 3.0.0",
 "solana-sdk-ids 3.0.0",
 "solana-serialize-utils 3.0.0",
 "solana-sysvar-id 3.0.0",
]

[[package]]
name = "solana-keccak-hasher"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7aeb957fbd42a451b99235df4942d96db7ef678e8d5061ef34c9b34cae12f79"
dependencies = [
 "sha3",
 "solana-define-syscall 2.3.0",
 "solana-hash 2.3.0",
 "solana-sanitize 2.2.1",
]

[[package]]
name = "solana-keypair"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3dbb7042c2e0c561afa07242b2099d55c57bd1b1da3b6476932197d84e15e3e4"
dependencies = [
 "bs58",
 "ed25519-dalek",
 "ed25519-dalek-bip32",
 "rand 0.7.3",
 "solana-derivation-path 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-seed-derivable 2.2.1",
 "solana-seed-phrase 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "wasm-bindgen",
]

[[package]]
name = "solana-last-restart-slot"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a6360ac2fdc72e7463565cd256eedcf10d7ef0c28a1249d261ec168c1b55cdd"
dependencies = [
 "serde",
 "serde_derive",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-lattice-hash"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d68fe797e5626ac2acf330e294f659c236eb13cb98d58df0917ca5b681b9248b"
dependencies = [
 "base64 0.22.1",
 "blake3",
 "bs58",
 "bytemuck",
]

[[package]]
name = "solana-ledger"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be3ea16644a28e4545987b97d765de33607304360d1414e89acb3f57c478c97d"
dependencies = [
 "agave-feature-set",
 "agave-reserved-account-keys",
 "anyhow",
 "assert_matches",
 "bincode",
 "bitflags 2.9.1",
 "bzip2",
 "chrono",
 "chrono-humanize",
 "crossbeam-channel",
 "dashmap",
 "eager",
 "fs_extra",
 "futures 0.3.31",
 "itertools 0.12.1",
 "lazy-lru",
 "libc",
 "log",
 "lru",
 "mockall",
 "num_cpus",
 "num_enum",
 "prost",
 "qualifier_attr",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rayon",
 "reed-solomon-erasure",
 "rocksdb",
 "scopeguard",
 "serde",
 "serde_bytes",
 "sha2 0.10.9",
 "solana-account",
 "solana-account-decoder",
 "solana-accounts-db",
 "solana-address-lookup-table-interface",
 "solana-bpf-loader-program",
 "solana-clock",
 "solana-cost-model",
 "solana-entry",
 "solana-epoch-schedule",
 "solana-genesis-config",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-measure",
 "solana-message",
 "solana-metrics",
 "solana-native-token",
 "solana-net-utils",
 "solana-packet",
 "solana-perf",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-rayon-threadlimit",
 "solana-runtime",
 "solana-runtime-transaction",
 "solana-seed-derivable 2.2.1",
 "solana-sha256-hasher 2.2.1",
 "solana-shred-version",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-stake-interface",
 "solana-stake-program",
 "solana-storage-bigtable",
 "solana-storage-proto",
 "solana-streamer",
 "solana-svm",
 "solana-svm-transaction",
 "solana-system-interface",
 "solana-system-transaction",
 "solana-time-utils",
 "solana-timings",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status",
 "solana-vote",
 "solana-vote-program",
 "static_assertions",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "tar",
 "tempfile",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "trees",
]

[[package]]
name = "solana-loader-v2-interface"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8ab08006dad78ae7cd30df8eea0539e207d08d91eaefb3e1d49a446e1c49654"
dependencies = [
 "serde",
 "serde_bytes",
 "serde_derive",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-loader-v3-interface"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f7162a05b8b0773156b443bccd674ea78bb9aa406325b467ea78c06c99a63a2"
dependencies = [
 "serde",
 "serde_bytes",
 "serde_derive",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-system-interface",
]

[[package]]
name = "solana-loader-v4-interface"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "706a777242f1f39a83e2a96a2a6cb034cb41169c6ecbee2cf09cb873d9659e7e"
dependencies = [
 "serde",
 "serde_bytes",
 "serde_derive",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-system-interface",
]

[[package]]
name = "solana-loader-v4-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aa980c021f655b702c4282c10422ea0f7d10ee00347be45ad329d317a0af6f3"
dependencies = [
 "log",
 "qualifier_attr",
 "solana-account",
 "solana-bincode",
 "solana-bpf-loader-program",
 "solana-instruction 2.3.0",
 "solana-loader-v3-interface",
 "solana-loader-v4-interface",
 "solana-log-collector",
 "solana-measure",
 "solana-packet",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-sbpf",
 "solana-sdk-ids 2.2.1",
 "solana-transaction-context",
 "solana-type-overrides",
]

[[package]]
name = "solana-log-collector"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "045fb9230cb591f1a0f548932ed0ebc246a83aad5cc5e63f24e3ebddd3cf2a54"
dependencies = [
 "log",
]

[[package]]
name = "solana-logger"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db8e777ec1afd733939b532a42492d888ec7c88d8b4127a5d867eb45c6eb5cd5"
dependencies = [
 "env_logger 0.9.3",
 "lazy_static",
 "libc",
 "log",
 "signal-hook",
]

[[package]]
name = "solana-logger"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef7421d1092680d72065edbf5c7605856719b021bf5f173656c71febcdd5d003"
dependencies = [
 "env_logger 0.11.8",
 "lazy_static",
 "libc",
 "log",
 "signal-hook",
]

[[package]]
name = "solana-measure"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c17d033a8c8725e39998c51e36969fe079e8edb91a8019d3e941da9dc88c0ef3"

[[package]]
name = "solana-merkle-tree"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "110273c233259d002d49b4c0c0dc80b4959f1af7a076a714385022682fb1b48b"
dependencies = [
 "fast-math",
 "solana-hash 2.3.0",
 "solana-sha256-hasher 2.2.1",
]

[[package]]
name = "solana-message"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1796aabce376ff74bf89b78d268fa5e683d7d7a96a0a4e4813ec34de49d5314b"
dependencies = [
 "bincode",
 "blake3",
 "lazy_static",
 "serde",
 "serde_derive",
 "solana-bincode",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sanitize 2.2.1",
 "solana-sdk-ids 2.2.1",
 "solana-short-vec",
 "solana-system-interface",
 "solana-transaction-error 2.2.1",
 "wasm-bindgen",
]

[[package]]
name = "solana-metrics"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d41316e2545a117810f9507a382123a8af357a04e09adab189eead1fcc90c4b4"
dependencies = [
 "crossbeam-channel",
 "gethostname",
 "log",
 "reqwest 0.12.22",
 "solana-cluster-type",
 "solana-sha256-hasher 2.2.1",
 "solana-time-utils",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-msg"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f36a1a14399afaabc2781a1db09cb14ee4cc4ee5c7a5a3cfcc601811379a8092"
dependencies = [
 "solana-define-syscall 2.3.0",
]

[[package]]
name = "solana-msg"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "264275c556ea7e22b9d3f87d56305546a38d4eee8ec884f3b126236cb7dcbbb4"
dependencies = [
 "solana-define-syscall 3.0.0",
]

[[package]]
name = "solana-native-token"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "307fb2f78060995979e9b4f68f833623565ed4e55d3725f100454ce78a99a1a3"

[[package]]
name = "solana-net-utils"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdbf5df25bd50e6e7b1f448b04d8cf7157ad153588beae15e03b02a9741dd942"
dependencies = [
 "anyhow",
 "bincode",
 "bytes",
 "itertools 0.12.1",
 "log",
 "nix",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "socket2 0.5.10",
 "solana-serde",
 "tokio",
 "url 2.5.4",
]

[[package]]
name = "solana-nohash-hasher"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b8a731ed60e89177c8a7ab05fe0f1511cedd3e70e773f288f9de33a9cfdc21e"

[[package]]
name = "solana-nonce"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "703e22eb185537e06204a5bd9d509b948f0066f2d1d814a6f475dafb3ddf1325"
dependencies = [
 "serde",
 "serde_derive",
 "solana-fee-calculator",
 "solana-hash 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sha256-hasher 2.2.1",
]

[[package]]
name = "solana-nonce-account"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cde971a20b8dbf60144d6a84439dda86b5466e00e2843091fe731083cda614da"
dependencies = [
 "solana-account",
 "solana-hash 2.3.0",
 "solana-nonce",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-offchain-message"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b526398ade5dea37f1f147ce55dae49aa017a5d7326606359b0445ca8d946581"
dependencies = [
 "num_enum",
 "solana-hash 2.3.0",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-sanitize 2.2.1",
 "solana-sha256-hasher 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
]

[[package]]
name = "solana-packet"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "004f2d2daf407b3ec1a1ca5ec34b3ccdfd6866dd2d3c7d0715004a96e4b6d127"
dependencies = [
 "bincode",
 "bitflags 2.9.1",
 "cfg_eval",
 "serde",
 "serde_derive",
 "serde_with",
]

[[package]]
name = "solana-perf"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea9454d4e98821fa127d4d3c4fd1459419da327ec6c092e669d4ea06144de172"
dependencies = [
 "ahash 0.8.11",
 "bincode",
 "bv",
 "bytes",
 "caps",
 "curve25519-dalek 4.1.3",
 "dlopen2",
 "fnv",
 "libc",
 "log",
 "nix",
 "rand 0.8.5",
 "rayon",
 "serde",
 "solana-hash 2.3.0",
 "solana-message",
 "solana-metrics",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-rayon-threadlimit",
 "solana-sdk-ids 2.2.1",
 "solana-short-vec",
 "solana-signature 2.3.0",
 "solana-time-utils",
]

[[package]]
name = "solana-poh"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52904972df1cf056dc79b55d4500d24a7760ec188729777aa4bbc967bb2fafe3"
dependencies = [
 "core_affinity",
 "crossbeam-channel",
 "log",
 "qualifier_attr",
 "solana-clock",
 "solana-entry",
 "solana-hash 2.3.0",
 "solana-ledger",
 "solana-measure",
 "solana-metrics",
 "solana-poh-config",
 "solana-pubkey 2.4.0",
 "solana-runtime",
 "solana-time-utils",
 "solana-transaction",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-poh-config"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d650c3b4b9060082ac6b0efbbb66865089c58405bfb45de449f3f2b91eccee75"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "solana-poseidon"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65143c77c1d4864c05e238f25b7d41b5a14b4d56352afab38fe89d97a78fff7f"
dependencies = [
 "ark-bn254",
 "light-poseidon",
 "solana-define-syscall 2.3.0",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-precompile-error"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d87b2c1f5de77dfe2b175ee8dd318d196aaca4d0f66f02842f80c852811f9f8"
dependencies = [
 "num-traits",
 "solana-decode-error",
]

[[package]]
name = "solana-precompiles"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a460ab805ec063802105b463ecb5eb02c3ffe469e67a967eea8a6e778e0bc06"
dependencies = [
 "lazy_static",
 "solana-ed25519-program",
 "solana-feature-set",
 "solana-message",
 "solana-precompile-error",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-secp256k1-program",
 "solana-secp256r1-program",
]

[[package]]
name = "solana-presigner"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81a57a24e6a4125fc69510b6774cd93402b943191b6cddad05de7281491c90fe"
dependencies = [
 "solana-pubkey 2.4.0",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
]

[[package]]
name = "solana-program"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98eca145bd3545e2fbb07166e895370576e47a00a7d824e325390d33bf467210"
dependencies = [
 "bincode",
 "blake3",
 "borsh 0.10.4",
 "borsh 1.5.7",
 "bs58",
 "bytemuck",
 "console_error_panic_hook",
 "console_log",
 "getrandom 0.2.15",
 "lazy_static",
 "log",
 "memoffset",
 "num-bigint 0.4.6",
 "num-derive",
 "num-traits",
 "rand 0.8.5",
 "serde",
 "serde_bytes",
 "serde_derive",
 "solana-account-info 2.3.0",
 "solana-address-lookup-table-interface",
 "solana-atomic-u64 2.2.1",
 "solana-big-mod-exp",
 "solana-bincode",
 "solana-blake3-hasher",
 "solana-borsh 2.2.1",
 "solana-clock",
 "solana-cpi",
 "solana-decode-error",
 "solana-define-syscall 2.3.0",
 "solana-epoch-rewards",
 "solana-epoch-schedule",
 "solana-example-mocks",
 "solana-feature-gate-interface",
 "solana-fee-calculator",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-instructions-sysvar 2.2.2",
 "solana-keccak-hasher",
 "solana-last-restart-slot",
 "solana-loader-v2-interface",
 "solana-loader-v3-interface",
 "solana-loader-v4-interface",
 "solana-message",
 "solana-msg 2.2.1",
 "solana-native-token",
 "solana-nonce",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-program-memory 2.3.1",
 "solana-program-option 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sanitize 2.2.1",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-secp256k1-recover",
 "solana-serde-varint",
 "solana-serialize-utils 2.2.1",
 "solana-sha256-hasher 2.2.1",
 "solana-short-vec",
 "solana-slot-hashes",
 "solana-slot-history",
 "solana-stable-layout",
 "solana-stake-interface",
 "solana-system-interface",
 "solana-sysvar",
 "solana-sysvar-id 2.2.1",
 "solana-vote-interface",
 "thiserror 2.0.12",
 "wasm-bindgen",
]

[[package]]
name = "solana-program-entrypoint"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32ce041b1a0ed275290a5008ee1a4a6c48f5054c8a3d78d313c08958a06aedbd"
dependencies = [
 "solana-account-info 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-program-error"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8ae2c1a8d0d4ae865882d5770a7ebca92bab9c685e43f0461682c6c05a35bfa"
dependencies = [
 "borsh 1.5.7",
 "num-traits",
 "serde",
 "serde_derive",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-program-error"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1af32c995a7b692a915bb7414d5f8e838450cf7c70414e763d8abcae7b51f28"
dependencies = [
 "borsh 1.5.7",
]

[[package]]
name = "solana-program-memory"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a5426090c6f3fd6cfdc10685322fede9ca8e5af43cd6a59e98bfe4e91671712"
dependencies = [
 "solana-define-syscall 2.3.0",
]

[[package]]
name = "solana-program-memory"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10e5660c60749c7bfb30b447542529758e4dbcecd31b1e8af1fdc92e2bdde90a"
dependencies = [
 "solana-define-syscall 3.0.0",
]

[[package]]
name = "solana-program-option"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc677a2e9bc616eda6dbdab834d463372b92848b2bfe4a1ed4e4b4adba3397d0"

[[package]]
name = "solana-program-option"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e7b4ddb464f274deb4a497712664c3b612e3f5f82471d4e47710fc4ab1c3095"

[[package]]
name = "solana-program-pack"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "319f0ef15e6e12dc37c597faccb7d62525a509fec5f6975ecb9419efddeb277b"
dependencies = [
 "solana-program-error 2.2.1",
]

[[package]]
name = "solana-program-pack"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c169359de21f6034a63ebf96d6b380980307df17a8d371344ff04a883ec4e9d0"
dependencies = [
 "solana-program-error 3.0.0",
]

[[package]]
name = "solana-program-runtime"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faaed80488a55ba4a5a124b264ef6a807a1225b1753f781cbdf6ea114e5f41a8"
dependencies = [
 "base64 0.22.1",
 "bincode",
 "enum-iterator",
 "itertools 0.12.1",
 "log",
 "percentage",
 "rand 0.8.5",
 "serde",
 "solana-account",
 "solana-clock",
 "solana-epoch-rewards",
 "solana-epoch-schedule",
 "solana-fee-structure",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-last-restart-slot",
 "solana-log-collector",
 "solana-measure",
 "solana-metrics",
 "solana-program-entrypoint",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sbpf",
 "solana-sdk-ids 2.2.1",
 "solana-slot-hashes",
 "solana-stable-layout",
 "solana-svm-callback",
 "solana-svm-feature-set",
 "solana-system-interface",
 "solana-sysvar",
 "solana-sysvar-id 2.2.1",
 "solana-timings",
 "solana-transaction-context",
 "solana-type-overrides",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-program-test"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3fa89c04f924bc7bf5a40244074b0151ac63dc77ffe261290aacb39d0f85a96"
dependencies = [
 "agave-feature-set",
 "assert_matches",
 "async-trait",
 "base64 0.22.1",
 "bincode",
 "chrono-humanize",
 "crossbeam-channel",
 "log",
 "serde",
 "solana-account",
 "solana-account-info 2.3.0",
 "solana-accounts-db",
 "solana-banks-client",
 "solana-banks-interface",
 "solana-banks-server",
 "solana-clock",
 "solana-commitment-config",
 "solana-compute-budget",
 "solana-epoch-rewards",
 "solana-epoch-schedule",
 "solana-fee-calculator",
 "solana-genesis-config",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-loader-v3-interface",
 "solana-log-collector",
 "solana-logger 2.3.1",
 "solana-message",
 "solana-msg 2.2.1",
 "solana-native-token",
 "solana-poh-config",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-runtime",
 "solana-sbpf",
 "solana-sdk-ids 2.2.1",
 "solana-signer 2.2.1",
 "solana-stable-layout",
 "solana-stake-interface",
 "solana-svm",
 "solana-system-interface",
 "solana-sysvar",
 "solana-sysvar-id 2.2.1",
 "solana-timings",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "solana-vote-program",
 "spl-generic-token",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-pubkey"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b62adb9c3261a052ca1f999398c388f1daf558a1b492f60a6d9e64857db4ff1"
dependencies = [
 "borsh 0.10.4",
 "borsh 1.5.7",
 "bytemuck",
 "bytemuck_derive",
 "curve25519-dalek 4.1.3",
 "five8",
 "five8_const",
 "getrandom 0.2.15",
 "js-sys",
 "num-traits",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "solana-atomic-u64 2.2.1",
 "solana-decode-error",
 "solana-define-syscall 2.3.0",
 "solana-sanitize 2.2.1",
 "solana-sha256-hasher 2.2.1",
 "wasm-bindgen",
]

[[package]]
name = "solana-pubkey"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8909d399deb0851aa524420beeb5646b115fd253ef446e35fe4504c904da3941"
dependencies = [
 "solana-address",
]

[[package]]
name = "solana-pubsub-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8ea65fb00df1f934d372a3762f16c5d1423dc9e4ab9d2548ed6c7774ea108d0"
dependencies = [
 "crossbeam-channel",
 "futures-util",
 "http 0.2.12",
 "log",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account-decoder-client-types",
 "solana-clock",
 "solana-pubkey 2.4.0",
 "solana-rpc-client-types",
 "solana-signature 2.3.0",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tokio-tungstenite",
 "tungstenite",
 "url 2.5.4",
]

[[package]]
name = "solana-quic-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35498861e85147221f995b01fa51c09feddf3eb3ded472b759ca43c772750c1c"
dependencies = [
 "async-lock",
 "async-trait",
 "futures 0.3.31",
 "itertools 0.12.1",
 "log",
 "quinn",
 "quinn-proto",
 "rustls 0.23.29",
 "solana-connection-cache",
 "solana-keypair",
 "solana-measure",
 "solana-metrics",
 "solana-net-utils",
 "solana-pubkey 2.4.0",
 "solana-quic-definitions",
 "solana-rpc-client-api",
 "solana-signer 2.2.1",
 "solana-streamer",
 "solana-tls-utils",
 "solana-transaction-error 2.2.1",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-quic-definitions"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e606feac5110eb5d8afaa43ccaeea3ec49ccec36773387930b5ba545e745aea2"
dependencies = [
 "solana-keypair",
]

[[package]]
name = "solana-rayon-threadlimit"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7920b328da6207a84d1381f9a1b18f7a86af42feef91944cdb59bffd4ad74d14"
dependencies = [
 "num_cpus",
]

[[package]]
name = "solana-remote-wallet"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c1e7c96838831b0fc7f5c6f7620c91bc718b8feec183f13b843750e22bc6863"
dependencies = [
 "console 0.15.11",
 "dialoguer",
 "hidapi",
 "log",
 "num-derive",
 "num-traits",
 "parking_lot 0.12.3",
 "qstring",
 "semver",
 "solana-derivation-path 2.2.1",
 "solana-offchain-message",
 "solana-pubkey 2.4.0",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "thiserror 2.0.12",
 "uriparse",
]

[[package]]
name = "solana-rent"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1aea8fdea9de98ca6e8c2da5827707fb3842833521b528a713810ca685d2480"
dependencies = [
 "serde",
 "serde_derive",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-rent-collector"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c1e19f5d5108b0d824244425e43bc78bbb9476e2199e979b0230c9f632d3bf4"
dependencies = [
 "serde",
 "serde_derive",
 "solana-account",
 "solana-clock",
 "solana-epoch-schedule",
 "solana-genesis-config",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-rent-debits"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f6f9113c6003492e74438d1288e30cffa8ccfdc2ef7b49b9e816d8034da18cd"
dependencies = [
 "solana-pubkey 2.4.0",
 "solana-reward-info",
]

[[package]]
name = "solana-reserved-account-keys"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b293f4246626c0e0a991531f08848a713ada965612e99dc510963f04d12cae7"
dependencies = [
 "lazy_static",
 "solana-feature-set",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-reward-info"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18205b69139b1ae0ab8f6e11cdcb627328c0814422ad2482000fa2ca54ae4a2f"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "solana-rpc"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "208aeee7fbf23db4e6735e757ff59e492531f86c590a4031cea389c7f21e989f"
dependencies = [
 "agave-feature-set",
 "base64 0.22.1",
 "bincode",
 "bs58",
 "crossbeam-channel",
 "dashmap",
 "itertools 0.12.1",
 "jsonrpc-core",
 "jsonrpc-core-client",
 "jsonrpc-derive",
 "jsonrpc-http-server",
 "jsonrpc-pubsub",
 "libc",
 "log",
 "rayon",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
 "soketto",
 "solana-account",
 "solana-account-decoder",
 "solana-accounts-db",
 "solana-client",
 "solana-clock",
 "solana-commitment-config",
 "solana-entry",
 "solana-epoch-info",
 "solana-epoch-rewards-hasher",
 "solana-epoch-schedule",
 "solana-faucet",
 "solana-genesis-config",
 "solana-gossip",
 "solana-hash 2.3.0",
 "solana-keypair",
 "solana-ledger",
 "solana-measure",
 "solana-message",
 "solana-metrics",
 "solana-native-token",
 "solana-perf",
 "solana-poh",
 "solana-poh-config",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-quic-definitions",
 "solana-rayon-threadlimit",
 "solana-rpc-client-api",
 "solana-runtime",
 "solana-runtime-transaction",
 "solana-send-transaction-service",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-slot-history",
 "solana-stake-program",
 "solana-storage-bigtable",
 "solana-streamer",
 "solana-svm",
 "solana-system-interface",
 "solana-system-transaction",
 "solana-sysvar",
 "solana-time-utils",
 "solana-tpu-client",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status",
 "solana-validator-exit",
 "solana-version",
 "solana-vote",
 "solana-vote-program",
 "spl-generic-token",
 "spl-token",
 "spl-token-2022 8.0.1",
 "stream-cancel",
 "thiserror 2.0.12",
 "tokio",
 "tokio-util 0.7.15",
]

[[package]]
name = "solana-rpc-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3e48d54d2155b7442a3e3a34fcdf7aa5c0d40fd4f68789eb99ec8f899b549ba"
dependencies = [
 "async-trait",
 "base64 0.22.1",
 "bincode",
 "bs58",
 "futures 0.3.31",
 "indicatif",
 "log",
 "reqwest 0.12.22",
 "reqwest-middleware",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account",
 "solana-account-decoder-client-types",
 "solana-clock",
 "solana-commitment-config",
 "solana-epoch-info",
 "solana-epoch-schedule",
 "solana-feature-gate-interface",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-rpc-client-api",
 "solana-signature 2.3.0",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status-client-types",
 "solana-version",
 "solana-vote-interface",
 "tokio",
]

[[package]]
name = "solana-rpc-client-api"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8710855b7342efc5fd9951461aeabaa0631a4b1a24dfef5644edf76283b6f37c"
dependencies = [
 "anyhow",
 "jsonrpc-core",
 "reqwest 0.12.22",
 "reqwest-middleware",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account-decoder-client-types",
 "solana-clock",
 "solana-rpc-client-types",
 "solana-signer 2.2.1",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status-client-types",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-rpc-client-nonce-utils"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "582f8b6b0404d6dca8064ebfefd310c1d183d33a018a89844e82ef0c28824671"
dependencies = [
 "solana-account",
 "solana-commitment-config",
 "solana-hash 2.3.0",
 "solana-message",
 "solana-nonce",
 "solana-pubkey 2.4.0",
 "solana-rpc-client",
 "solana-sdk-ids 2.2.1",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-rpc-client-types"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fe9fd3064c2bb096ec8ec94ceae3a33b3a998b58bbbf28156e114de41cc945c"
dependencies = [
 "base64 0.22.1",
 "bs58",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account",
 "solana-account-decoder-client-types",
 "solana-clock",
 "solana-commitment-config",
 "solana-fee-calculator",
 "solana-inflation",
 "solana-pubkey 2.4.0",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status-client-types",
 "solana-version",
 "spl-generic-token",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-runtime"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df5ca69813c6b9efd937291609841ee21d793dc5c40fdb9a064c0d0e0323da44"
dependencies = [
 "agave-feature-set",
 "agave-precompiles",
 "agave-reserved-account-keys",
 "ahash 0.8.11",
 "aquamarine",
 "arrayref",
 "assert_matches",
 "base64 0.22.1",
 "bincode",
 "blake3",
 "bv",
 "bytemuck",
 "bzip2",
 "crossbeam-channel",
 "dashmap",
 "dir-diff",
 "flate2",
 "fnv",
 "im",
 "itertools 0.12.1",
 "libc",
 "log",
 "lz4",
 "memmap2 0.9.7",
 "mockall",
 "modular-bitfield",
 "num-derive",
 "num-traits",
 "num_cpus",
 "num_enum",
 "percentage",
 "qualifier_attr",
 "rand 0.8.5",
 "rayon",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_with",
 "solana-account",
 "solana-account-info 2.3.0",
 "solana-accounts-db",
 "solana-address-lookup-table-interface",
 "solana-bpf-loader-program",
 "solana-bucket-map",
 "solana-builtins",
 "solana-client-traits",
 "solana-clock",
 "solana-commitment-config",
 "solana-compute-budget",
 "solana-compute-budget-instruction",
 "solana-compute-budget-interface",
 "solana-cost-model",
 "solana-cpi",
 "solana-ed25519-program",
 "solana-epoch-info",
 "solana-epoch-rewards-hasher",
 "solana-epoch-schedule",
 "solana-feature-gate-interface",
 "solana-fee",
 "solana-fee-calculator",
 "solana-fee-structure",
 "solana-genesis-config",
 "solana-hard-forks",
 "solana-hash 2.3.0",
 "solana-inflation",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-lattice-hash",
 "solana-loader-v3-interface",
 "solana-loader-v4-interface",
 "solana-measure",
 "solana-message",
 "solana-metrics",
 "solana-native-token",
 "solana-nohash-hasher",
 "solana-nonce",
 "solana-nonce-account",
 "solana-packet",
 "solana-perf",
 "solana-poh-config",
 "solana-precompile-error",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-rayon-threadlimit",
 "solana-rent",
 "solana-rent-collector",
 "solana-rent-debits",
 "solana-reward-info",
 "solana-runtime-transaction",
 "solana-sdk-ids 2.2.1",
 "solana-secp256k1-program",
 "solana-seed-derivable 2.2.1",
 "solana-serde",
 "solana-sha256-hasher 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-slot-hashes",
 "solana-slot-history",
 "solana-stake-interface",
 "solana-stake-program",
 "solana-svm",
 "solana-svm-callback",
 "solana-svm-rent-collector",
 "solana-svm-transaction",
 "solana-system-interface",
 "solana-system-transaction",
 "solana-sysvar",
 "solana-sysvar-id 2.2.1",
 "solana-time-utils",
 "solana-timings",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status-client-types",
 "solana-unified-scheduler-logic",
 "solana-version",
 "solana-vote",
 "solana-vote-interface",
 "solana-vote-program",
 "spl-generic-token",
 "static_assertions",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "symlink",
 "tar",
 "tempfile",
 "thiserror 2.0.12",
 "zstd",
]

[[package]]
name = "solana-runtime-transaction"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0345883ad085433c4c06c829a2316e8a6eec30b6a176ec518b0d4cd26f15aed5"
dependencies = [
 "agave-transaction-view",
 "log",
 "solana-compute-budget",
 "solana-compute-budget-instruction",
 "solana-hash 2.3.0",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-signature 2.3.0",
 "solana-svm-transaction",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-sanitize"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61f1bc1357b8188d9c4a3af3fc55276e56987265eb7ad073ae6f8180ee54cecf"

[[package]]
name = "solana-sanitize"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "927e833259588ac8f860861db0f6e2668c3cc46d917798ade116858960acfe8a"

[[package]]
name = "solana-sbpf"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "474a2d95dc819898ded08d24f29642d02189d3e1497bbb442a92a3997b7eb55f"
dependencies = [
 "byteorder",
 "combine 3.8.1",
 "hash32",
 "libc",
 "log",
 "rand 0.8.5",
 "rustc-demangle",
 "thiserror 2.0.12",
 "winapi 0.3.9",
]

[[package]]
name = "solana-sdk"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cc0e4a7635b902791c44b6581bfb82f3ada32c5bc0929a64f39fe4bb384c86a"
dependencies = [
 "bincode",
 "bs58",
 "getrandom 0.1.16",
 "js-sys",
 "serde",
 "serde_json",
 "solana-account",
 "solana-bn254",
 "solana-client-traits",
 "solana-cluster-type",
 "solana-commitment-config",
 "solana-compute-budget-interface",
 "solana-decode-error",
 "solana-derivation-path 2.2.1",
 "solana-ed25519-program",
 "solana-epoch-info",
 "solana-epoch-rewards-hasher",
 "solana-feature-set",
 "solana-fee-structure",
 "solana-genesis-config",
 "solana-hard-forks",
 "solana-inflation",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-message",
 "solana-native-token",
 "solana-nonce-account",
 "solana-offchain-message",
 "solana-packet",
 "solana-poh-config",
 "solana-precompile-error",
 "solana-precompiles",
 "solana-presigner",
 "solana-program",
 "solana-program-memory 2.3.1",
 "solana-pubkey 2.4.0",
 "solana-quic-definitions",
 "solana-rent-collector",
 "solana-rent-debits",
 "solana-reserved-account-keys",
 "solana-reward-info",
 "solana-sanitize 2.2.1",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-secp256k1-program",
 "solana-secp256k1-recover",
 "solana-secp256r1-program",
 "solana-seed-derivable 2.2.1",
 "solana-seed-phrase 2.2.1",
 "solana-serde",
 "solana-serde-varint",
 "solana-short-vec",
 "solana-shred-version",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-system-transaction",
 "solana-time-utils",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "solana-validator-exit",
 "thiserror 2.0.12",
 "wasm-bindgen",
]

[[package]]
name = "solana-sdk-ids"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c5d8b9cc68d5c88b062a33e23a6466722467dde0035152d8fb1afbcdf350a5f"
dependencies = [
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-sdk-ids"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1b6d6aaf60669c592838d382266b173881c65fb1cdec83b37cb8ce7cb89f9ad"
dependencies = [
 "solana-pubkey 3.0.0",
]

[[package]]
name = "solana-sdk-macro"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86280da8b99d03560f6ab5aca9de2e38805681df34e0bb8f238e69b29433b9df"
dependencies = [
 "bs58",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "solana-secp256k1-program"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0a1caa972414cc78122c32bdae65ac5fe89df7db598585a5cde19d16a20280a"
dependencies = [
 "bincode",
 "digest 0.10.7",
 "libsecp256k1",
 "serde",
 "serde_derive",
 "sha3",
 "solana-feature-set",
 "solana-instruction 2.3.0",
 "solana-precompile-error",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-secp256k1-recover"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baa3120b6cdaa270f39444f5093a90a7b03d296d362878f7a6991d6de3bbe496"
dependencies = [
 "borsh 1.5.7",
 "libsecp256k1",
 "solana-define-syscall 2.3.0",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-secp256r1-program"
version = "2.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce0ae46da3071a900f02d367d99b2f3058fe2e90c5062ac50c4f20cfedad8f0f"
dependencies = [
 "bytemuck",
 "openssl",
 "solana-feature-set",
 "solana-instruction 2.3.0",
 "solana-precompile-error",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-security-txt"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "468aa43b7edb1f9b7b7b686d5c3aeb6630dc1708e86e31343499dd5c4d775183"

[[package]]
name = "solana-seed-derivable"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3beb82b5adb266c6ea90e5cf3967235644848eac476c5a1f2f9283a143b7c97f"
dependencies = [
 "solana-derivation-path 2.2.1",
]

[[package]]
name = "solana-seed-derivable"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff7bdb72758e3bec33ed0e2658a920f1f35dfb9ed576b951d20d63cb61ecd95c"
dependencies = [
 "solana-derivation-path 3.0.0",
]

[[package]]
name = "solana-seed-phrase"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36187af2324f079f65a675ec22b31c24919cb4ac22c79472e85d819db9bbbc15"
dependencies = [
 "hmac 0.12.1",
 "pbkdf2 0.11.0",
 "sha2 0.10.9",
]

[[package]]
name = "solana-seed-phrase"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc905b200a95f2ea9146e43f2a7181e3aeb55de6bc12afb36462d00a3c7310de"
dependencies = [
 "hmac 0.12.1",
 "pbkdf2 0.11.0",
 "sha2 0.10.9",
]

[[package]]
name = "solana-send-transaction-service"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "775d4bf50c03ad604bba6dd65d3565dff9fda47255fbdd607b6462a86eb7f94c"
dependencies = [
 "async-trait",
 "crossbeam-channel",
 "itertools 0.12.1",
 "log",
 "solana-client",
 "solana-clock",
 "solana-connection-cache",
 "solana-hash 2.3.0",
 "solana-keypair",
 "solana-measure",
 "solana-metrics",
 "solana-nonce-account",
 "solana-pubkey 2.4.0",
 "solana-quic-definitions",
 "solana-runtime",
 "solana-signature 2.3.0",
 "solana-time-utils",
 "solana-tpu-client-next",
 "tokio",
 "tokio-util 0.7.15",
]

[[package]]
name = "solana-serde"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1931484a408af466e14171556a47adaa215953c7f48b24e5f6b0282763818b04"
dependencies = [
 "serde",
]

[[package]]
name = "solana-serde-varint"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a7e155eba458ecfb0107b98236088c3764a09ddf0201ec29e52a0be40857113"
dependencies = [
 "serde",
]

[[package]]
name = "solana-serialize-utils"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "817a284b63197d2b27afdba829c5ab34231da4a9b4e763466a003c40ca4f535e"
dependencies = [
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sanitize 2.2.1",
]

[[package]]
name = "solana-serialize-utils"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7665da4f6e07b58c93ef6aaf9fb6a923fd11b0922ffc53ba74c3cadfa490f26"
dependencies = [
 "solana-instruction-error",
 "solana-pubkey 3.0.0",
 "solana-sanitize 3.0.0",
]

[[package]]
name = "solana-sha256-hasher"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0037386961c0d633421f53560ad7c80675c0447cba4d1bb66d60974dd486c7ea"
dependencies = [
 "sha2 0.10.9",
 "solana-define-syscall 2.3.0",
 "solana-hash 2.3.0",
]

[[package]]
name = "solana-sha256-hasher"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9b912ba6f71cb202c0c3773ec77bf898fa9fe0c78691a2d6859b3b5b8954719"
dependencies = [
 "sha2 0.10.9",
 "solana-define-syscall 3.0.0",
 "solana-hash 3.0.0",
]

[[package]]
name = "solana-short-vec"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c54c66f19b9766a56fa0057d060de8378676cb64987533fa088861858fc5a69"
dependencies = [
 "serde",
]

[[package]]
name = "solana-shred-version"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afd3db0461089d1ad1a78d9ba3f15b563899ca2386351d38428faa5350c60a98"
dependencies = [
 "solana-hard-forks",
 "solana-hash 2.3.0",
 "solana-sha256-hasher 2.2.1",
]

[[package]]
name = "solana-signature"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64c8ec8e657aecfc187522fc67495142c12f35e55ddeca8698edbb738b8dbd8c"
dependencies = [
 "ed25519-dalek",
 "five8",
 "rand 0.8.5",
 "serde",
 "serde-big-array",
 "serde_derive",
 "solana-sanitize 2.2.1",
]

[[package]]
name = "solana-signature"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4bb8057cc0e9f7b5e89883d49de6f407df655bb6f3a71d0b7baf9986a2218fd9"
dependencies = [
 "five8",
 "solana-sanitize 3.0.0",
]

[[package]]
name = "solana-signer"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c41991508a4b02f021c1342ba00bcfa098630b213726ceadc7cb032e051975b"
dependencies = [
 "solana-pubkey 2.4.0",
 "solana-signature 2.3.0",
 "solana-transaction-error 2.2.1",
]

[[package]]
name = "solana-signer"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5bfea97951fee8bae0d6038f39a5efcb6230ecdfe33425ac75196d1a1e3e3235"
dependencies = [
 "solana-pubkey 3.0.0",
 "solana-signature 3.1.0",
 "solana-transaction-error 3.0.0",
]

[[package]]
name = "solana-slot-hashes"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c8691982114513763e88d04094c9caa0376b867a29577939011331134c301ce"
dependencies = [
 "serde",
 "serde_derive",
 "solana-hash 2.3.0",
 "solana-sdk-ids 2.2.1",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-slot-history"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97ccc1b2067ca22754d5283afb2b0126d61eae734fc616d23871b0943b0d935e"
dependencies = [
 "bv",
 "serde",
 "serde_derive",
 "solana-sdk-ids 2.2.1",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-stable-layout"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f14f7d02af8f2bc1b5efeeae71bc1c2b7f0f65cd75bcc7d8180f2c762a57f54"
dependencies = [
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-stake-interface"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5269e89fde216b4d7e1d1739cf5303f8398a1ff372a81232abbee80e554a838c"
dependencies = [
 "borsh 0.10.4",
 "borsh 1.5.7",
 "num-traits",
 "serde",
 "serde_derive",
 "solana-clock",
 "solana-cpi",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-system-interface",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-stake-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54ee3fde30acddc028581afdf16de9b89091c2bab7b0b5651b7d473273d9a5d5"
dependencies = [
 "agave-feature-set",
 "bincode",
 "log",
 "solana-account",
 "solana-bincode",
 "solana-clock",
 "solana-config-program-client",
 "solana-genesis-config",
 "solana-instruction 2.3.0",
 "solana-log-collector",
 "solana-native-token",
 "solana-packet",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-stake-interface",
 "solana-sysvar",
 "solana-transaction-context",
 "solana-type-overrides",
 "solana-vote-interface",
]

[[package]]
name = "solana-storage-bigtable"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9572d456b99cd320715cd262c7448b93cc4b8424e65d336c3547bb51c33c1ce"
dependencies = [
 "agave-reserved-account-keys",
 "backoff",
 "bincode",
 "bytes",
 "bzip2",
 "enum-iterator",
 "flate2",
 "futures 0.3.31",
 "goauth",
 "http 0.2.12",
 "hyper 0.14.32",
 "hyper-proxy",
 "log",
 "openssl",
 "prost",
 "prost-types",
 "serde",
 "serde_derive",
 "smpl_jwt",
 "solana-clock",
 "solana-message",
 "solana-metrics",
 "solana-pubkey 2.4.0",
 "solana-serde",
 "solana-signature 2.3.0",
 "solana-storage-proto",
 "solana-time-utils",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status",
 "thiserror 2.0.12",
 "tokio",
 "tonic",
 "zstd",
]

[[package]]
name = "solana-storage-proto"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47cc921c7daf2bf3b5722b0e0889775950011f623a92bdd6fc277f51945f7918"
dependencies = [
 "bincode",
 "bs58",
 "prost",
 "protobuf-src",
 "serde",
 "solana-account-decoder",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-serde",
 "solana-signature 2.3.0",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status",
 "tonic-build",
]

[[package]]
name = "solana-streamer"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d7b33dfd0a99f0537154b451d9f70274c431d85a997c6e0128409b413f8dffd"
dependencies = [
 "async-channel",
 "bytes",
 "crossbeam-channel",
 "dashmap",
 "futures 0.3.31",
 "futures-util",
 "governor",
 "histogram",
 "indexmap 2.10.0",
 "itertools 0.12.1",
 "libc",
 "log",
 "nix",
 "pem",
 "percentage",
 "quinn",
 "quinn-proto",
 "rand 0.8.5",
 "rustls 0.23.29",
 "smallvec",
 "socket2 0.5.10",
 "solana-keypair",
 "solana-measure",
 "solana-metrics",
 "solana-net-utils",
 "solana-packet",
 "solana-perf",
 "solana-pubkey 2.4.0",
 "solana-quic-definitions",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-time-utils",
 "solana-tls-utils",
 "solana-transaction-error 2.2.1",
 "solana-transaction-metrics-tracker",
 "thiserror 2.0.12",
 "tokio",
 "tokio-util 0.7.15",
 "x509-parser",
]

[[package]]
name = "solana-svm"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bb3f23bd59479b086521d5ebc2074857a21b9fd7f13f3561cf0a784a860eb2e"
dependencies = [
 "ahash 0.8.11",
 "itertools 0.12.1",
 "log",
 "percentage",
 "serde",
 "serde_derive",
 "solana-account",
 "solana-clock",
 "solana-fee-structure",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-instructions-sysvar 2.2.2",
 "solana-loader-v3-interface",
 "solana-loader-v4-interface",
 "solana-loader-v4-program",
 "solana-log-collector",
 "solana-measure",
 "solana-message",
 "solana-nonce",
 "solana-nonce-account",
 "solana-program-entrypoint",
 "solana-program-pack 2.2.1",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-rent-collector",
 "solana-rent-debits",
 "solana-sdk-ids 2.2.1",
 "solana-slot-hashes",
 "solana-svm-callback",
 "solana-svm-feature-set",
 "solana-svm-rent-collector",
 "solana-svm-transaction",
 "solana-system-interface",
 "solana-sysvar-id 2.2.1",
 "solana-timings",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "solana-type-overrides",
 "spl-generic-token",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-svm-callback"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4aa58b3b9410f377b572cb2e7fd1910900295bce47b9dcdbcbc42569a2b192c9"
dependencies = [
 "solana-account",
 "solana-precompile-error",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-svm-feature-set"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c75d9e63442629ecf438f9fbb5647b92c1d7f66c5eb1d46bcfa4eb34cd457f86"

[[package]]
name = "solana-svm-rent-collector"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0012625e8569e94c044bed0c466ee6dab9af5a821d279933fbc343e38b842cc9"
dependencies = [
 "solana-account",
 "solana-clock",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-rent-collector",
 "solana-sdk-ids 2.2.1",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
]

[[package]]
name = "solana-svm-transaction"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfc3d7bb7e0d630d28295b1a51b240a32922f598b6a72b3b821c7d6c9463702e"
dependencies = [
 "solana-hash 2.3.0",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-signature 2.3.0",
 "solana-transaction",
]

[[package]]
name = "solana-system-interface"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94d7c18cb1a91c6be5f5a8ac9276a1d7c737e39a21beba9ea710ab4b9c63bc90"
dependencies = [
 "js-sys",
 "num-traits",
 "serde",
 "serde_derive",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "wasm-bindgen",
]

[[package]]
name = "solana-system-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17a208cce4205cac8386ea2750ab8cd453f469a0ef55769cf0e4abf78ace735b"
dependencies = [
 "bincode",
 "log",
 "serde",
 "serde_derive",
 "solana-account",
 "solana-bincode",
 "solana-fee-calculator",
 "solana-instruction 2.3.0",
 "solana-log-collector",
 "solana-nonce",
 "solana-nonce-account",
 "solana-packet",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-system-interface",
 "solana-sysvar",
 "solana-transaction-context",
 "solana-type-overrides",
]

[[package]]
name = "solana-system-transaction"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5bd98a25e5bcba8b6be8bcbb7b84b24c2a6a8178d7fb0e3077a916855ceba91a"
dependencies = [
 "solana-hash 2.3.0",
 "solana-keypair",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-signer 2.2.1",
 "solana-system-interface",
 "solana-transaction",
]

[[package]]
name = "solana-sysvar"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8c3595f95069f3d90f275bb9bd235a1973c4d059028b0a7f81baca2703815db"
dependencies = [
 "base64 0.22.1",
 "bincode",
 "bytemuck",
 "bytemuck_derive",
 "lazy_static",
 "serde",
 "serde_derive",
 "solana-account-info 2.3.0",
 "solana-clock",
 "solana-define-syscall 2.3.0",
 "solana-epoch-rewards",
 "solana-epoch-schedule",
 "solana-fee-calculator",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-instructions-sysvar 2.2.2",
 "solana-last-restart-slot",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-program-memory 2.3.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sanitize 2.2.1",
 "solana-sdk-ids 2.2.1",
 "solana-sdk-macro",
 "solana-slot-hashes",
 "solana-slot-history",
 "solana-stake-interface",
 "solana-sysvar-id 2.2.1",
]

[[package]]
name = "solana-sysvar-id"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5762b273d3325b047cfda250787f8d796d781746860d5d0a746ee29f3e8812c1"
dependencies = [
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-sysvar-id"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5051bc1a16d5d96a96bc33b5b2ec707495c48fe978097bdaba68d3c47987eb32"
dependencies = [
 "solana-pubkey 3.0.0",
 "solana-sdk-ids 3.0.0",
]

[[package]]
name = "solana-test-validator"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db02e03f888267b86a33d271dc3a2e01d98cb8a67320a5b358bc2b8de772c58c"
dependencies = [
 "agave-feature-set",
 "base64 0.22.1",
 "bincode",
 "crossbeam-channel",
 "log",
 "serde_derive",
 "serde_json",
 "solana-account",
 "solana-accounts-db",
 "solana-cli-output",
 "solana-clock",
 "solana-cluster-type",
 "solana-commitment-config",
 "solana-compute-budget",
 "solana-core",
 "solana-epoch-schedule",
 "solana-feature-gate-interface",
 "solana-fee-calculator",
 "solana-geyser-plugin-manager",
 "solana-gossip",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-ledger",
 "solana-loader-v3-interface",
 "solana-logger 2.3.1",
 "solana-message",
 "solana-native-token",
 "solana-net-utils",
 "solana-program-test",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-rpc",
 "solana-rpc-client",
 "solana-rpc-client-api",
 "solana-runtime",
 "solana-sdk-ids 2.2.1",
 "solana-signer 2.2.1",
 "solana-streamer",
 "solana-tpu-client",
 "solana-validator-exit",
 "tokio",
]

[[package]]
name = "solana-thin-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597916274841b9491e1057034fcca199c8c6dcb2437295194608c91da15fb545"
dependencies = [
 "bincode",
 "log",
 "rayon",
 "solana-account",
 "solana-client-traits",
 "solana-clock",
 "solana-commitment-config",
 "solana-connection-cache",
 "solana-epoch-info",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-message",
 "solana-pubkey 2.4.0",
 "solana-rpc-client",
 "solana-rpc-client-api",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-system-interface",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
]

[[package]]
name = "solana-time-utils"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6af261afb0e8c39252a04d026e3ea9c405342b08c871a2ad8aa5448e068c784c"

[[package]]
name = "solana-timings"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e6b2450d6c51c25b57cc067e0ab93015feb27347c34a81ddd540f9979a2b125"
dependencies = [
 "eager",
 "enum-iterator",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "solana-tls-utils"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "261b7aeeca06bbbe05f8c82913c2415389efc46435de9932a71839439a614c2f"
dependencies = [
 "rustls 0.23.29",
 "solana-keypair",
 "solana-pubkey 2.4.0",
 "solana-signer 2.2.1",
 "x509-parser",
]

[[package]]
name = "solana-tpu-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6b70691bb3ef570f9f9fbf1fcfda34618d1eb59dcab2fae2d77e87eaca0a76f"
dependencies = [
 "async-trait",
 "bincode",
 "futures-util",
 "indexmap 2.10.0",
 "indicatif",
 "log",
 "rayon",
 "solana-client-traits",
 "solana-clock",
 "solana-commitment-config",
 "solana-connection-cache",
 "solana-epoch-schedule",
 "solana-measure",
 "solana-message",
 "solana-net-utils",
 "solana-pubkey 2.4.0",
 "solana-pubsub-client",
 "solana-quic-definitions",
 "solana-rpc-client",
 "solana-rpc-client-api",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-tpu-client-next"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ec22dff31f318350328d5ba7208933b1f7489b5e089c2fb1621c4f2b7371b4a"
dependencies = [
 "async-trait",
 "log",
 "lru",
 "quinn",
 "rustls 0.23.29",
 "solana-clock",
 "solana-connection-cache",
 "solana-keypair",
 "solana-measure",
 "solana-metrics",
 "solana-quic-definitions",
 "solana-rpc-client",
 "solana-streamer",
 "solana-time-utils",
 "solana-tls-utils",
 "solana-tpu-client",
 "thiserror 2.0.12",
 "tokio",
 "tokio-util 0.7.15",
]

[[package]]
name = "solana-transaction"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "abec848d081beb15a324c633cd0e0ab33033318063230389895cae503ec9b544"
dependencies = [
 "bincode",
 "serde",
 "serde_derive",
 "solana-bincode",
 "solana-feature-set",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-message",
 "solana-precompiles",
 "solana-pubkey 2.4.0",
 "solana-sanitize 2.2.1",
 "solana-sdk-ids 2.2.1",
 "solana-short-vec",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-system-interface",
 "solana-transaction-error 2.2.1",
 "wasm-bindgen",
]

[[package]]
name = "solana-transaction-context"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a3005a53f202a6b1b21068733748c7a0c2e4e8f5ff4a25032d59df7f5deec0b"
dependencies = [
 "bincode",
 "serde",
 "serde_derive",
 "solana-account",
 "solana-instruction 2.3.0",
 "solana-instructions-sysvar 2.2.2",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
]

[[package]]
name = "solana-transaction-error"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "222a9dc8fdb61c6088baab34fc3a8b8473a03a7a5fd404ed8dd502fa79b67cb1"
dependencies = [
 "serde",
 "serde_derive",
 "solana-instruction 2.3.0",
 "solana-sanitize 2.2.1",
]

[[package]]
name = "solana-transaction-error"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4222065402340d7e6aec9dc3e54d22992ddcf923d91edcd815443c2bfca3144a"
dependencies = [
 "solana-instruction-error",
 "solana-sanitize 3.0.0",
]

[[package]]
name = "solana-transaction-metrics-tracker"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0b52d7bdfb64dba22d1129b93a2f959ef645561b777f0c5897019f5754250b6"
dependencies = [
 "base64 0.22.1",
 "bincode",
 "log",
 "rand 0.8.5",
 "solana-packet",
 "solana-perf",
 "solana-short-vec",
 "solana-signature 2.3.0",
]

[[package]]
name = "solana-transaction-status"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c6d87ced5a2b5d4c84e21d73c73df60e7d0f0d0485f556c0d4bd0fd5f2ca07f"
dependencies = [
 "Inflector",
 "agave-reserved-account-keys",
 "base64 0.22.1",
 "bincode",
 "borsh 1.5.7",
 "bs58",
 "log",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account-decoder",
 "solana-address-lookup-table-interface",
 "solana-clock",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-loader-v2-interface",
 "solana-loader-v3-interface",
 "solana-message",
 "solana-program-option 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-reward-info",
 "solana-sdk-ids 2.2.1",
 "solana-signature 2.3.0",
 "solana-stake-interface",
 "solana-system-interface",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-transaction-status-client-types",
 "solana-vote-interface",
 "spl-associated-token-account",
 "spl-memo",
 "spl-token",
 "spl-token-2022 8.0.1",
 "spl-token-group-interface 0.6.0",
 "spl-token-metadata-interface 0.7.0",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-transaction-status-client-types"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4796a3c2bdbef21867114aaa200e04fe0a7208d81d1c2bf3e99fabc285bd925"
dependencies = [
 "base64 0.22.1",
 "bincode",
 "bs58",
 "serde",
 "serde_derive",
 "serde_json",
 "solana-account-decoder-client-types",
 "solana-commitment-config",
 "solana-message",
 "solana-reward-info",
 "solana-signature 2.3.0",
 "solana-transaction",
 "solana-transaction-context",
 "solana-transaction-error 2.2.1",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-turbine"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c593dd3ab231ec70abb00e153933c60f5a39039b698fcfb05c2a8d4012e8633"
dependencies = [
 "agave-feature-set",
 "agave-xdp",
 "bincode",
 "bytes",
 "caps",
 "crossbeam-channel",
 "futures 0.3.31",
 "itertools 0.12.1",
 "lazy-lru",
 "log",
 "lru",
 "quinn",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rayon",
 "rustls 0.23.29",
 "solana-clock",
 "solana-cluster-type",
 "solana-entry",
 "solana-gossip",
 "solana-hash 2.3.0",
 "solana-keypair",
 "solana-ledger",
 "solana-measure",
 "solana-metrics",
 "solana-native-token",
 "solana-net-utils",
 "solana-perf",
 "solana-poh",
 "solana-pubkey 2.4.0",
 "solana-quic-client",
 "solana-rayon-threadlimit",
 "solana-rpc",
 "solana-rpc-client-api",
 "solana-runtime",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-streamer",
 "solana-system-transaction",
 "solana-time-utils",
 "solana-tls-utils",
 "solana-transaction-error 2.2.1",
 "static_assertions",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-type-overrides"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38f826f38dba90fcd24832edb75394a7140c5816b2416d93aad50edf33a0a93a"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "solana-udp-client"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb8fdccd1bd4972bdd632370ee0e353f1eec4c9ee7c49bac70a5f804b6eb1816"
dependencies = [
 "async-trait",
 "solana-connection-cache",
 "solana-keypair",
 "solana-net-utils",
 "solana-streamer",
 "solana-transaction-error 2.2.1",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "solana-unified-scheduler-logic"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96fb2a227e734de3200c12a5f57ad75dd9af1f798ec8ead564b6fe923ad9bcc1"
dependencies = [
 "assert_matches",
 "solana-pubkey 2.4.0",
 "solana-runtime-transaction",
 "solana-transaction",
 "static_assertions",
 "unwrap_none",
]

[[package]]
name = "solana-unified-scheduler-pool"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72daba7fe36fd40d9cf82344f4c2b5d039f1133707fa69ceb12c35302163e7f0"
dependencies = [
 "agave-banking-stage-ingress-types",
 "aquamarine",
 "assert_matches",
 "crossbeam-channel",
 "dashmap",
 "derive-where",
 "derive_more 1.0.0",
 "dyn-clone",
 "log",
 "qualifier_attr",
 "scopeguard",
 "solana-clock",
 "solana-cost-model",
 "solana-ledger",
 "solana-poh",
 "solana-pubkey 2.4.0",
 "solana-runtime",
 "solana-runtime-transaction",
 "solana-svm",
 "solana-timings",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-unified-scheduler-logic",
 "static_assertions",
 "trait-set",
 "unwrap_none",
 "vec_extract_if_polyfill",
]

[[package]]
name = "solana-validator-exit"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bbf6d7a3c0b28dd5335c52c0e9eae49d0ae489a8f324917faf0ded65a812c1d"

[[package]]
name = "solana-version"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f94a680221a357f8f69d7190b6152be6d5a19289bee1092d362493ecf351506b"
dependencies = [
 "agave-feature-set",
 "rand 0.8.5",
 "semver",
 "serde",
 "serde_derive",
 "solana-sanitize 2.2.1",
 "solana-serde-varint",
]

[[package]]
name = "solana-vote"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "979db3da03376f1cb179db2fb8e21caa753028b3c1945ff40c78726793d7a331"
dependencies = [
 "itertools 0.12.1",
 "log",
 "serde",
 "serde_derive",
 "solana-account",
 "solana-bincode",
 "solana-clock",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-packet",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-serialize-utils 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-svm-transaction",
 "solana-transaction",
 "solana-vote-interface",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-vote-interface"
version = "2.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef4f08746f154458f28b98330c0d55cb431e2de64ee4b8efc98dcbe292e0672b"
dependencies = [
 "bincode",
 "num-derive",
 "num-traits",
 "serde",
 "serde_derive",
 "solana-clock",
 "solana-decode-error",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-serde-varint",
 "solana-serialize-utils 2.2.1",
 "solana-short-vec",
 "solana-system-interface",
]

[[package]]
name = "solana-vote-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55a0e62cf9bc0483152abac9338d067a961f2cc3f4bd8b321129d15db499bb64"
dependencies = [
 "agave-feature-set",
 "bincode",
 "log",
 "num-derive",
 "num-traits",
 "serde",
 "serde_derive",
 "solana-account",
 "solana-bincode",
 "solana-clock",
 "solana-epoch-schedule",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-packet",
 "solana-program-runtime",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-signer 2.2.1",
 "solana-slot-hashes",
 "solana-transaction",
 "solana-transaction-context",
 "solana-vote-interface",
 "thiserror 2.0.12",
]

[[package]]
name = "solana-wen-restart"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f7717e6bdd8c21188489bf02c98d0ee342a3871e4067f02784313b5396ae136"
dependencies = [
 "anyhow",
 "log",
 "prost",
 "prost-build",
 "prost-types",
 "protobuf-src",
 "rayon",
 "solana-clock",
 "solana-entry",
 "solana-gossip",
 "solana-hash 2.3.0",
 "solana-ledger",
 "solana-pubkey 2.4.0",
 "solana-runtime",
 "solana-shred-version",
 "solana-time-utils",
 "solana-timings",
 "solana-vote",
 "solana-vote-interface",
 "solana-vote-program",
]

[[package]]
name = "solana-zk-elgamal-proof-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c857b47345e9017b7906579b5742381de76a9b4785f5d9d3a997a42211825245"
dependencies = [
 "agave-feature-set",
 "bytemuck",
 "num-derive",
 "num-traits",
 "solana-instruction 2.3.0",
 "solana-log-collector",
 "solana-program-runtime",
 "solana-sdk-ids 2.2.1",
 "solana-zk-sdk 2.3.4",
]

[[package]]
name = "solana-zk-sdk"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c13cbe908b9142274d5cdedc57b6bbc705181d05c7a2c7df21a76ad93463119"
dependencies = [
 "aes-gcm-siv",
 "base64 0.22.1",
 "bincode",
 "bytemuck",
 "bytemuck_derive",
 "curve25519-dalek 4.1.3",
 "itertools 0.12.1",
 "js-sys",
 "merlin",
 "num-derive",
 "num-traits",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "serde_json",
 "sha3",
 "solana-derivation-path 2.2.1",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-seed-derivable 2.2.1",
 "solana-seed-phrase 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "subtle",
 "thiserror 2.0.12",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "solana-zk-sdk"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9602bcb1f7af15caef92b91132ec2347e1c51a72ecdbefdaefa3eac4b8711475"
dependencies = [
 "aes-gcm-siv",
 "base64 0.22.1",
 "bincode",
 "bytemuck",
 "bytemuck_derive",
 "curve25519-dalek 4.1.3",
 "getrandom 0.2.15",
 "itertools 0.12.1",
 "js-sys",
 "merlin",
 "num-derive",
 "num-traits",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "serde_json",
 "sha3",
 "solana-derivation-path 3.0.0",
 "solana-instruction 3.0.0",
 "solana-pubkey 3.0.0",
 "solana-sdk-ids 3.0.0",
 "solana-seed-derivable 3.0.0",
 "solana-seed-phrase 3.0.0",
 "solana-signature 3.1.0",
 "solana-signer 3.0.0",
 "subtle",
 "thiserror 2.0.12",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "solana-zk-token-proof-program"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3441d519b441143d4f8a44d958a160c868e22abc42e007d428264b4392267bc9"
dependencies = [
 "agave-feature-set",
 "bytemuck",
 "num-derive",
 "num-traits",
 "solana-instruction 2.3.0",
 "solana-log-collector",
 "solana-program-runtime",
 "solana-sdk-ids 2.2.1",
 "solana-zk-token-sdk",
]

[[package]]
name = "solana-zk-token-sdk"
version = "2.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c75b31849ca786c2da9c4d1a7292b33d5f8e697626b9eb5a53adf759a8409f6e"
dependencies = [
 "aes-gcm-siv",
 "base64 0.22.1",
 "bincode",
 "bytemuck",
 "bytemuck_derive",
 "curve25519-dalek 4.1.3",
 "itertools 0.12.1",
 "merlin",
 "num-derive",
 "num-traits",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "serde_json",
 "sha3",
 "solana-curve25519",
 "solana-derivation-path 2.2.1",
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-seed-derivable 2.2.1",
 "solana-seed-phrase 2.2.1",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "subtle",
 "thiserror 2.0.12",
 "zeroize",
]

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "spinning_top"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d96d2d1d716fb500937168cc09353ffdc7a012be8475ac7308e1bdf0e3923300"
dependencies = [
 "lock_api",
]

[[package]]
name = "spl-associated-token-account"
version = "7.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae179d4a26b3c7a20c839898e6aed84cb4477adf108a366c95532f058aea041b"
dependencies = [
 "borsh 1.5.7",
 "num-derive",
 "num-traits",
 "solana-program",
 "spl-associated-token-account-client",
 "spl-token",
 "spl-token-2022 8.0.1",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-associated-token-account-client"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6f8349dbcbe575f354f9a533a21f272f3eb3808a49e2fdc1c34393b88ba76cb"
dependencies = [
 "solana-instruction 2.3.0",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "spl-discriminator"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7398da23554a31660f17718164e31d31900956054f54f52d5ec1be51cb4f4b3"
dependencies = [
 "bytemuck",
 "solana-program-error 2.2.1",
 "solana-sha256-hasher 2.2.1",
 "spl-discriminator-derive",
]

[[package]]
name = "spl-discriminator"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d48cc11459e265d5b501534144266620289720b4c44522a47bc6b63cd295d2f3"
dependencies = [
 "bytemuck",
 "solana-program-error 3.0.0",
 "solana-sha256-hasher 3.0.0",
 "spl-discriminator-derive",
]

[[package]]
name = "spl-discriminator-derive"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9e8418ea6269dcfb01c712f0444d2c75542c04448b480e87de59d2865edc750"
dependencies = [
 "quote",
 "spl-discriminator-syn",
 "syn 2.0.99",
]

[[package]]
name = "spl-discriminator-syn"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c1f05593b7ca9eac7caca309720f2eafb96355e037e6d373b909a80fe7b69b9"
dependencies = [
 "proc-macro2",
 "quote",
 "sha2 0.10.9",
 "syn 2.0.99",
 "thiserror 1.0.69",
]

[[package]]
name = "spl-elgamal-registry"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65edfeed09cd4231e595616aa96022214f9c9d2be02dea62c2b30d5695a6833a"
dependencies = [
 "bytemuck",
 "solana-account-info 2.3.0",
 "solana-cpi",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-system-interface",
 "solana-sysvar",
 "solana-zk-sdk 2.3.4",
 "spl-pod 0.5.1",
 "spl-token-confidential-transfer-proof-extraction 0.3.0",
]

[[package]]
name = "spl-elgamal-registry"
version = "0.3.0"
dependencies = [
 "bytemuck",
 "solana-account-info 2.3.0",
 "solana-cpi",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-security-txt",
 "solana-system-interface",
 "solana-sysvar",
 "solana-zk-sdk 2.3.4",
 "spl-pod 0.5.1",
 "spl-token-confidential-transfer-proof-extraction 0.4.1",
]

[[package]]
name = "spl-generic-token"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "741a62a566d97c58d33f9ed32337ceedd4e35109a686e31b1866c5dfa56abddc"
dependencies = [
 "bytemuck",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "spl-instruction-padding"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5352d4c4a6d455fc96320342aeab9f522f057e2666ebe40b2e079d054339ab8f"
dependencies = [
 "num_enum",
 "solana-account-info 2.3.0",
 "solana-cpi",
 "solana-instruction 2.3.0",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "spl-memo"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f09647c0974e33366efeb83b8e2daebb329f0420149e74d3a4bd2c08cf9f7cb"
dependencies = [
 "solana-account-info 2.3.0",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
]

[[package]]
name = "spl-pod"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d994afaf86b779104b4a95ba9ca75b8ced3fdb17ee934e38cb69e72afbe17799"
dependencies = [
 "borsh 1.5.7",
 "bytemuck",
 "bytemuck_derive",
 "num-derive",
 "num-traits",
 "serde",
 "solana-decode-error",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-program-option 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-zk-sdk 2.3.4",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-pod"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1233fdecd7461611d69bb87bc2e95af742df47291975d21232a0be8217da9de"
dependencies = [
 "borsh 1.5.7",
 "bytemuck",
 "bytemuck_derive",
 "num-derive",
 "num-traits",
 "num_enum",
 "serde",
 "solana-program-error 3.0.0",
 "solana-program-option 3.0.0",
 "solana-pubkey 3.0.0",
 "solana-zk-sdk 4.0.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-program-error"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9cdebc8b42553070b75aa5106f071fef2eb798c64a7ec63375da4b1f058688c6"
dependencies = [
 "num-derive",
 "num-traits",
 "solana-decode-error",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "spl-program-error-derive",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-program-error-derive"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2539e259c66910d78593475540e8072f0b10f0f61d7607bbf7593899ed52d0"
dependencies = [
 "proc-macro2",
 "quote",
 "sha2 0.10.9",
 "syn 2.0.99",
]

[[package]]
name = "spl-record"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1288810a85bbe7e62ee3c6f7b8119e8c1016e90351411d12e4132e98c7ca7344"
dependencies = [
 "bytemuck",
 "num-derive",
 "num-traits",
 "solana-account-info 2.3.0",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "thiserror 1.0.69",
]

[[package]]
name = "spl-tlv-account-resolution"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1408e961215688715d5a1063cbdcf982de225c45f99c82b4f7d7e1dd22b998d7"
dependencies = [
 "bytemuck",
 "num-derive",
 "num-traits",
 "solana-account-info 2.3.0",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "spl-discriminator 0.4.1",
 "spl-pod 0.5.1",
 "spl-program-error",
 "spl-type-length-value 0.8.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "053067c6a82c705004f91dae058b11b4780407e9ccd6799dc9e7d0fab5f242da"
dependencies = [
 "arrayref",
 "bytemuck",
 "num-derive",
 "num-traits",
 "num_enum",
 "solana-account-info 2.3.0",
 "solana-cpi",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-program-memory 2.3.1",
 "solana-program-option 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-sysvar",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-2022"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31f0dfbb079eebaee55e793e92ca5f433744f4b71ee04880bfd6beefba5973e5"
dependencies = [
 "arrayref",
 "bytemuck",
 "num-derive",
 "num-traits",
 "num_enum",
 "solana-account-info 2.3.0",
 "solana-clock",
 "solana-cpi",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-native-token",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-program-memory 2.3.1",
 "solana-program-option 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-security-txt",
 "solana-system-interface",
 "solana-sysvar",
 "solana-zk-sdk 2.3.4",
 "spl-elgamal-registry 0.2.0",
 "spl-memo",
 "spl-pod 0.5.1",
 "spl-token",
 "spl-token-confidential-transfer-ciphertext-arithmetic 0.3.1",
 "spl-token-confidential-transfer-proof-extraction 0.3.0",
 "spl-token-confidential-transfer-proof-generation 0.4.1",
 "spl-token-group-interface 0.6.0",
 "spl-token-metadata-interface 0.7.0",
 "spl-transfer-hook-interface",
 "spl-type-length-value 0.8.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-2022"
version = "9.0.0"
dependencies = [
 "arrayref",
 "base64 0.22.1",
 "bytemuck",
 "lazy_static",
 "num-derive",
 "num-traits",
 "num_enum",
 "proptest",
 "serde",
 "serde_json",
 "serde_with",
 "serial_test",
 "solana-account",
 "solana-account-info 2.3.0",
 "solana-clock",
 "solana-cpi",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-keypair",
 "solana-msg 2.2.1",
 "solana-program-entrypoint",
 "solana-program-error 2.2.1",
 "solana-program-memory 2.3.1",
 "solana-program-option 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-program-test",
 "solana-pubkey 2.4.0",
 "solana-rent",
 "solana-sdk-ids 2.2.1",
 "solana-security-txt",
 "solana-signer 2.2.1",
 "solana-system-interface",
 "solana-sysvar",
 "solana-transaction",
 "solana-transaction-error 2.2.1",
 "solana-zk-sdk 2.3.4",
 "spl-elgamal-registry 0.3.0",
 "spl-memo",
 "spl-pod 0.5.1",
 "spl-tlv-account-resolution",
 "spl-token-2022-interface 1.0.0",
 "spl-token-confidential-transfer-ciphertext-arithmetic 0.3.1",
 "spl-token-confidential-transfer-proof-extraction 0.4.1",
 "spl-token-confidential-transfer-proof-generation 0.4.1",
 "spl-token-group-interface 0.6.0",
 "spl-token-metadata-interface 0.7.0",
 "spl-transfer-hook-interface",
 "test-case",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-2022-interface"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62d7ae2ee6b856f8ddcbdc3b3a9f4d2141582bbe150f93e5298ee97e0251fa04"
dependencies = [
 "arrayref",
 "base64 0.22.1",
 "bytemuck",
 "num-derive",
 "num-traits",
 "num_enum",
 "serde",
 "serde_with",
 "solana-account-info 2.3.0",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-program-option 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-zk-sdk 2.3.4",
 "spl-pod 0.5.1",
 "spl-token-confidential-transfer-proof-extraction 0.4.1",
 "spl-token-confidential-transfer-proof-generation 0.4.1",
 "spl-token-group-interface 0.6.0",
 "spl-token-metadata-interface 0.7.0",
 "spl-type-length-value 0.8.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-2022-interface"
version = "2.0.0"
dependencies = [
 "arrayref",
 "base64 0.22.1",
 "bytemuck",
 "num-derive",
 "num-traits",
 "num_enum",
 "proptest",
 "serde",
 "serde_json",
 "serde_with",
 "solana-account-info 3.0.0",
 "solana-instruction 3.0.0",
 "solana-program-error 3.0.0",
 "solana-program-option 3.0.0",
 "solana-program-pack 3.0.0",
 "solana-pubkey 3.0.0",
 "solana-sdk-ids 3.0.0",
 "solana-zk-sdk 4.0.0",
 "spl-pod 0.7.1",
 "spl-token-2022-interface 2.0.0",
 "spl-token-confidential-transfer-proof-extraction 0.5.0",
 "spl-token-confidential-transfer-proof-generation 0.5.0",
 "spl-token-group-interface 0.7.1",
 "spl-token-interface",
 "spl-token-metadata-interface 0.8.0",
 "spl-type-length-value 0.9.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-cli"
version = "5.4.0"
dependencies = [
 "assert_cmd",
 "base64 0.22.1",
 "clap 3.2.25",
 "console 0.16.0",
 "futures 0.3.31",
 "libtest-mimic",
 "serde",
 "serde_derive",
 "serde_json",
 "serial_test",
 "solana-account-decoder",
 "solana-clap-v3-utils",
 "solana-cli-config",
 "solana-cli-output",
 "solana-client",
 "solana-logger 3.0.0",
 "solana-remote-wallet",
 "solana-sdk",
 "solana-sdk-ids 2.2.1",
 "solana-system-interface",
 "solana-test-validator",
 "solana-transaction-status",
 "spl-associated-token-account-client",
 "spl-memo",
 "spl-pod 0.5.1",
 "spl-token",
 "spl-token-2022 9.0.0",
 "spl-token-client",
 "spl-token-confidential-transfer-proof-generation 0.4.1",
 "spl-token-group-interface 0.6.0",
 "spl-token-metadata-interface 0.7.0",
 "strum 0.27.2",
 "strum_macros 0.27.2",
 "tempfile",
 "tokio",
 "walkdir",
]

[[package]]
name = "spl-token-client"
version = "0.17.0"
dependencies = [
 "async-trait",
 "bincode",
 "borsh 1.5.7",
 "bytemuck",
 "futures 0.3.31",
 "futures-util",
 "solana-account",
 "solana-banks-client",
 "solana-banks-interface",
 "solana-cli-output",
 "solana-compute-budget-interface",
 "solana-hash 2.3.0",
 "solana-instruction 2.3.0",
 "solana-message",
 "solana-packet",
 "solana-program",
 "solana-program-error 2.2.1",
 "solana-program-pack 2.2.1",
 "solana-program-test",
 "solana-pubkey 2.4.0",
 "solana-rpc-client",
 "solana-rpc-client-api",
 "solana-sdk",
 "solana-signature 2.3.0",
 "solana-signer 2.2.1",
 "solana-system-interface",
 "solana-transaction",
 "spl-associated-token-account",
 "spl-associated-token-account-client",
 "spl-elgamal-registry 0.3.0",
 "spl-instruction-padding",
 "spl-memo",
 "spl-pod 0.5.1",
 "spl-record",
 "spl-tlv-account-resolution",
 "spl-token",
 "spl-token-2022 9.0.0",
 "spl-token-client",
 "spl-token-confidential-transfer-proof-extraction 0.4.1",
 "spl-token-confidential-transfer-proof-generation 0.4.1",
 "spl-token-group-interface 0.6.0",
 "spl-token-metadata-interface 0.7.0",
 "spl-transfer-hook-interface",
 "test-case",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "spl-token-confidential-transfer-ciphertext-arithmetic"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cddd52bfc0f1c677b41493dafa3f2dbbb4b47cf0990f08905429e19dc8289b35"
dependencies = [
 "base64 0.22.1",
 "bytemuck",
 "solana-curve25519",
 "solana-zk-sdk 2.3.4",
]

[[package]]
name = "spl-token-confidential-transfer-ciphertext-arithmetic"
version = "0.4.0"
dependencies = [
 "base64 0.22.1",
 "bytemuck",
 "curve25519-dalek 4.1.3",
 "solana-curve25519",
 "solana-zk-sdk 4.0.0",
 "spl-token-confidential-transfer-proof-generation 0.5.0",
]

[[package]]
name = "spl-token-confidential-transfer-proof-extraction"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe2629860ff04c17bafa9ba4bed8850a404ecac81074113e1f840dbd0ebb7bd6"
dependencies = [
 "bytemuck",
 "solana-account-info 2.3.0",
 "solana-curve25519",
 "solana-instruction 2.3.0",
 "solana-instructions-sysvar 2.2.2",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-zk-sdk 2.3.4",
 "spl-pod 0.5.1",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-confidential-transfer-proof-extraction"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "512c85bdbbb4cbcc2038849a9e164c958b16541f252b53ea1a3933191c0a4a1a"
dependencies = [
 "bytemuck",
 "solana-account-info 2.3.0",
 "solana-curve25519",
 "solana-instruction 2.3.0",
 "solana-instructions-sysvar 2.2.2",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "solana-sdk-ids 2.2.1",
 "solana-zk-sdk 2.3.4",
 "spl-pod 0.5.1",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-confidential-transfer-proof-extraction"
version = "0.5.0"
dependencies = [
 "bytemuck",
 "solana-account-info 3.0.0",
 "solana-curve25519",
 "solana-instruction 3.0.0",
 "solana-instructions-sysvar 3.0.0",
 "solana-msg 3.0.0",
 "solana-program-error 3.0.0",
 "solana-pubkey 3.0.0",
 "solana-sdk-ids 3.0.0",
 "solana-zk-sdk 4.0.0",
 "spl-pod 0.7.1",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-confidential-transfer-proof-generation"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa27b9174bea869a7ebf31e0be6890bce90b1a4288bc2bbf24bd413f80ae3fde"
dependencies = [
 "curve25519-dalek 4.1.3",
 "solana-zk-sdk 2.3.4",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-confidential-transfer-proof-generation"
version = "0.5.0"
dependencies = [
 "curve25519-dalek 4.1.3",
 "solana-zk-sdk 4.0.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-confidential-transfer-proof-test"
version = "0.0.1"
dependencies = [
 "curve25519-dalek 4.1.3",
 "solana-zk-sdk 4.0.0",
 "spl-token-confidential-transfer-proof-extraction 0.5.0",
 "spl-token-confidential-transfer-proof-generation 0.5.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-group-interface"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5597b4cd76f85ce7cd206045b7dc22da8c25516573d42d267c8d1fd128db5129"
dependencies = [
 "bytemuck",
 "num-derive",
 "num-traits",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "spl-discriminator 0.4.1",
 "spl-pod 0.5.1",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-group-interface"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "452d0f758af20caaa10d9a6f7608232e000d4c74462f248540b3d2ddfa419776"
dependencies = [
 "bytemuck",
 "num-derive",
 "num-traits",
 "num_enum",
 "solana-instruction 3.0.0",
 "solana-program-error 3.0.0",
 "solana-pubkey 3.0.0",
 "spl-discriminator 0.5.1",
 "spl-pod 0.7.1",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-interface"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c564ac05a7c8d8b12e988a37d82695b5ba4db376d07ea98bc4882c81f96c7f3"
dependencies = [
 "arrayref",
 "bytemuck",
 "num-derive",
 "num-traits",
 "num_enum",
 "solana-instruction 3.0.0",
 "solana-program-error 3.0.0",
 "solana-program-option 3.0.0",
 "solana-program-pack 3.0.0",
 "solana-pubkey 3.0.0",
 "solana-sdk-ids 3.0.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-metadata-interface"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "304d6e06f0de0c13a621464b1fd5d4b1bebf60d15ca71a44d3839958e0da16ee"
dependencies = [
 "borsh 1.5.7",
 "num-derive",
 "num-traits",
 "solana-borsh 2.2.1",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "spl-discriminator 0.4.1",
 "spl-pod 0.5.1",
 "spl-type-length-value 0.8.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-token-metadata-interface"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c467c7c3bd056f8fe60119e7ec34ddd6f23052c2fa8f1f51999098063b72676"
dependencies = [
 "borsh 1.5.7",
 "num-derive",
 "num-traits",
 "solana-borsh 3.0.0",
 "solana-instruction 3.0.0",
 "solana-program-error 3.0.0",
 "solana-pubkey 3.0.0",
 "spl-discriminator 0.5.1",
 "spl-pod 0.7.1",
 "spl-type-length-value 0.9.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-transfer-hook-interface"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7e905b849b6aba63bde8c4badac944ebb6c8e6e14817029cbe1bc16829133bd"
dependencies = [
 "arrayref",
 "bytemuck",
 "num-derive",
 "num-traits",
 "solana-account-info 2.3.0",
 "solana-cpi",
 "solana-decode-error",
 "solana-instruction 2.3.0",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "solana-pubkey 2.4.0",
 "spl-discriminator 0.4.1",
 "spl-pod 0.5.1",
 "spl-program-error",
 "spl-tlv-account-resolution",
 "spl-type-length-value 0.8.0",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-type-length-value"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d417eb548214fa822d93f84444024b4e57c13ed6719d4dcc68eec24fb481e9f5"
dependencies = [
 "bytemuck",
 "num-derive",
 "num-traits",
 "solana-account-info 2.3.0",
 "solana-decode-error",
 "solana-msg 2.2.1",
 "solana-program-error 2.2.1",
 "spl-discriminator 0.4.1",
 "spl-pod 0.5.1",
 "thiserror 2.0.12",
]

[[package]]
name = "spl-type-length-value"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca20a1a19f4507a98ca4b28ff5ed54cac9b9d34ed27863e2bde50a3238f9a6ac"
dependencies = [
 "bytemuck",
 "num-derive",
 "num-traits",
 "num_enum",
 "solana-account-info 3.0.0",
 "solana-msg 3.0.0",
 "solana-program-error 3.0.0",
 "spl-discriminator 0.5.1",
 "spl-pod 0.7.1",
 "thiserror 2.0.12",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "stream-cancel"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f9fbf9bd71e4cf18d68a8a0951c0e5b7255920c0cd992c4ff51cddd6ef514a3"
dependencies = [
 "futures-core",
 "pin-project",
 "tokio",
]

[[package]]
name = "strsim"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ea5119cdb4c55b55d432abb513a0429384878c15dde60cc77b1c99de1a95a6a"

[[package]]
name = "strsim"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73473c0e59e6d5812c5dfe2a064a6444949f089e20eec9a2e5506596494e4623"

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"
dependencies = [
 "strum_macros 0.24.3",
]

[[package]]
name = "strum"
version = "0.27.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af23d6f6c1a224baef9d3f61e287d2761385a5b88fdab4eb4c6f11aeb54c4bcf"

[[package]]
name = "strum_macros"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"
dependencies = [
 "heck 0.4.1",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.109",
]

[[package]]
name = "strum_macros"
version = "0.27.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7695ce3845ea4b33927c055a39dc438a45b059f7c1b3d91d38d10355fb8cbca7"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "subtle"
version = "2.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c2bddecc57b384dee18652358fb23172facb8a2c51ccc10d74c157bdea3292"

[[package]]
name = "symlink"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7973cce6668464ea31f176d85b13c7ab3bba2cb3b77a2ed26abd7801688010a"

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.99"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e02e925281e18ffd9d640e234264753c43edc62d64b2d4cf898f1bc5e75f3fc2"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "sync_wrapper"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2047c6ded9c721764247e62cd3b03c09ffc529b2ba5b10ec482ae507a4a70160"

[[package]]
name = "sync_wrapper"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bf256ce5efdfa370213c1dabab5935a12e49f2c58d15e9eac2870d3b4f27263"
dependencies = [
 "futures-core",
]

[[package]]
name = "synstructure"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f36bdaa60a83aca3921b5259d5400cbf5e90fc51931376a9bd4a0eb79aa7210f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "unicode-xid",
]

[[package]]
name = "synstructure"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8af7666ab7b6390ab78131fb5b0fce11d6b7a6951602017c35fa82800708971"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "sys-info"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b3a0d0aba8bf96a0e1ddfdc352fc53b3df7f39318c71854910c3c4b024ae52c"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "sysctl"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "225e483f02d0ad107168dc57381a8a40c3aeea6abe47f37506931f861643cfa8"
dependencies = [
 "bitflags 1.3.2",
 "byteorder",
 "libc",
 "thiserror 1.0.69",
 "walkdir",
]

[[package]]
name = "system-configuration"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba3a3adc5c275d719af8cb4272ea1c4a6d668a777f37e115f6d11ddbc1c8e0e7"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation 0.9.4",
 "system-configuration-sys",
]

[[package]]
name = "system-configuration-sys"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75fb188eb626b924683e3b95e3a48e63551fcfb51949de2f06a9d91dbee93c9"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "tar"
version = "0.4.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d863878d212c87a19c1a610eb53bb01fe12951c0501cf5a0d65f724914a667a"
dependencies = [
 "filetime",
 "libc",
 "xattr",
]

[[package]]
name = "tarpc"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c38a012bed6fb9681d3bf71ffaa4f88f3b4b9ed3198cda6e4c8462d24d4bb80"
dependencies = [
 "anyhow",
 "fnv",
 "futures 0.3.31",
 "humantime",
 "opentelemetry",
 "pin-project",
 "rand 0.8.5",
 "serde",
 "static_assertions",
 "tarpc-plugins",
 "thiserror 1.0.69",
 "tokio",
 "tokio-serde",
 "tokio-util 0.6.10",
 "tracing",
 "tracing-opentelemetry",
]

[[package]]
name = "tarpc-plugins"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ee42b4e559f17bce0385ebf511a7beb67d5cc33c12c96b7f4e9789919d9c10f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "tempfile"
version = "3.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a64e3985349f2441a1a9ef0b853f869006c3855f2cda6862a94d26ebb9d6a1"
dependencies = [
 "fastrand",
 "getrandom 0.3.1",
 "once_cell",
 "rustix 1.0.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "termtree"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f50febec83f5ee1df3015341d8bd429f2d1cc62bcba7ea2076759d315084683"

[[package]]
name = "test-case"
version = "3.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb2550dd13afcd286853192af8601920d959b14c401fcece38071d53bf0768a8"
dependencies = [
 "test-case-macros",
]

[[package]]
name = "test-case-core"
version = "3.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adcb7fd841cd518e279be3d5a3eb0636409487998a4aff22f3de87b81e88384f"
dependencies = [
 "cfg-if 1.0.0",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "test-case-macros"
version = "3.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c89e72a01ed4c579669add59014b9a524d609c0c88c6a585ce37485879f6ffb"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
 "test-case-core",
]

[[package]]
name = "textwrap"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d326610f408c7a4eb6f51c37c330e496b08506c9457c9d34287ecc38809fb060"
dependencies = [
 "unicode-width 0.1.14",
]

[[package]]
name = "textwrap"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c13547615a44dc9c452a8a534638acdf07120d4b6847c8178705da06306a3057"

[[package]]
name = "thiserror"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6aaf5339b578ea85b50e080feb250a3e8ae8cfcdff9a461c9ec2904bc923f52"
dependencies = [
 "thiserror-impl 1.0.69",
]

[[package]]
name = "thiserror"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567b8a2dae586314f7be2a752ec7474332959c6460e02bde30d702a66d488708"
dependencies = [
 "thiserror-impl 2.0.12",
]

[[package]]
name = "thiserror-impl"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fee6c4efc90059e10f81e6d42c60a18f76588c3d74cb83a0b242a2b6c7504c1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "thiserror-impl"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7cf42b4507d8ea322120659672cf1b9dbb93f8f2d4ecfd6e51350ff5b17a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "thread_local"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9ef9bad013ada3808854ceac7b46812a6465ba368859a37e2100283d2d719c"
dependencies = [
 "cfg-if 1.0.0",
 "once_cell",
]

[[package]]
name = "tikv-jemalloc-sys"
version = "0.6.0****.0-1-ge13ca993e8ccb9ba9847cc330696e02839f328f7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd3c60906412afa9c2b5b5a48ca6a5abe5736aec9eb48ad05037a677e52e4e2d"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "tikv-jemallocator"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cec5ff18518d81584f477e9bfdf957f5bb0979b0bac3af4ca30b5b3ae2d2865"
dependencies = [
 "libc",
 "tikv-jemalloc-sys",
]

[[package]]
name = "time"
version = "0.3.39"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dad298b01a40a23aac4580b67e3dbedb7cc8402f3592d7f49469de2ea4aecdd8"
dependencies = [
 "deranged",
 "itoa",
 "num-conv",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "765c97a5b985b7c11d7bc27fa927dc4fe6af3a6dfb021d28deb60d3bf51e76ef"

[[package]]
name = "time-macros"
version = "0.2.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8093bc3e81c3bc5f7879de09619d06c9a5a5e45ca44dfeeb7225bae38005c5c"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tiny-bip39"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffc59cb9dfc85bb312c3a78fd6aa8a8582e310b0fa885d5bb877f6dcc601839d"
dependencies = [
 "anyhow",
 "hmac 0.8.1",
 "once_cell",
 "pbkdf2 0.4.0",
 "rand 0.7.3",
 "rustc-hash 1.1.0",
 "sha2 0.9.9",
 "thiserror 1.0.69",
 "unicode-normalization",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "tinystr"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9117f5d4db391c1cf6927e7bea3db74b9a1c1add8f7eda9ffd5364f40f57b82f"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "tinyvec"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09b3661f17e86524eccd4371ab0429194e0d7c008abb45f7a7495b1719463c71"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tokio"
version = "1.47.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89e49afdadebb872d3145a5638b59eb0691ea23e46ca484037cfab3b76b95038"
dependencies = [
 "backtrace",
 "bytes",
 "io-uring",
 "libc",
 "mio",
 "parking_lot 0.12.3",
 "pin-project-lite",
 "signal-hook-registry",
 "slab",
 "socket2 0.6.0",
 "tokio-macros",
 "windows-sys 0.59.0",
]

[[package]]
name = "tokio-io-timeout"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30b74022ada614a1b4834de765f9bb43877f910cc8ce4be40e89042c9223a8bf"
dependencies = [
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-macros"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e06d43f1345a3bcd39f6a56dbb7dcab2ba47e68e8ac134855e7e2bdbaf8cab8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "tokio-native-tls"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbae76ab933c85776efabc971569dd6119c580d8f5d448769dec1764bf796ef2"
dependencies = [
 "native-tls",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28327cf380ac148141087fbfb9de9d7bd4e84ab5d2c28fbc911d753de8a7081"
dependencies = [
 "rustls 0.21.12",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e727b36a1a0e8b74c376ac2211e40c2c8af09fb4013c60d910495810f008e9b"
dependencies = [
 "rustls 0.23.29",
 "tokio",
]

[[package]]
name = "tokio-serde"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "911a61637386b789af998ee23f50aa30d5fd7edcec8d6d3dedae5e5815205466"
dependencies = [
 "bincode",
 "bytes",
 "educe",
 "futures-core",
 "futures-sink",
 "pin-project",
 "serde",
 "serde_json",
]

[[package]]
name = "tokio-stream"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eca58d7bba4a75707817a2c44174253f9236b2d5fbd055602e9d5c07c139a047"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-tungstenite"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "212d5dcb2a1ce06d81107c3d0ffa3121fe974b73f068c8282cb1c32328113b6c"
dependencies = [
 "futures-util",
 "log",
 "rustls 0.21.12",
 "tokio",
 "tokio-rustls 0.24.1",
 "tungstenite",
 "webpki-roots 0.25.4",
]

[[package]]
name = "tokio-util"
version = "0.6.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36943ee01a6d67977dd3f84a5a1d2efeb4ada3a1ae771cadfaa535d9d9fc6507"
dependencies = [
 "bytes",
 "futures-core",
 "futures-sink",
 "log",
 "pin-project-lite",
 "slab",
 "tokio",
]

[[package]]
name = "tokio-util"
version = "0.7.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66a539a9ad6d5d281510d5bd368c973d636c02dbf8a67300bfb6b950696ad7df"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "toml"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f7f0dd8d50a853a531c426359045b1998f04219d88799810762cd4ad314234"
dependencies = [
 "serde",
]

[[package]]
name = "toml_datetime"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dd7358ecb8fc2f8d014bf86f6f638ce72ba252a2c3a2572f2a795f1d23efb41"

[[package]]
name = "toml_edit"
version = "0.22.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17b4795ff5edd201c7cd6dca065ae59972ce77d1b80fa0a84d94950ece7d1474"
dependencies = [
 "indexmap 2.10.0",
 "toml_datetime",
 "winnow",
]

[[package]]
name = "tonic"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3082666a3a6433f7f511c7192923fa1fe07c69332d3c6a2e6bb040b569199d5a"
dependencies = [
 "async-stream",
 "async-trait",
 "axum",
 "base64 0.21.7",
 "bytes",
 "futures-core",
 "futures-util",
 "h2",
 "http 0.2.12",
 "http-body 0.4.6",
 "hyper 0.14.32",
 "hyper-timeout",
 "percent-encoding 2.3.1",
 "pin-project",
 "prost",
 "rustls-pemfile",
 "tokio",
 "tokio-rustls 0.24.1",
 "tokio-stream",
 "tower 0.4.13",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tonic-build"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a6fdaae4c2c638bb70fe42803a26fbd6fc6ac8c72f5c59f67ecc2a2dcabf4b07"
dependencies = [
 "prettyplease",
 "proc-macro2",
 "prost-build",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "indexmap 1.9.3",
 "pin-project",
 "pin-project-lite",
 "rand 0.8.5",
 "slab",
 "tokio",
 "tokio-util 0.7.15",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d039ad9159c98b70ecfd540b2573b97f7f52c3e8d9f8ad57a24b916a536975f9"
dependencies = [
 "futures-core",
 "futures-util",
 "pin-project-lite",
 "sync_wrapper 1.0.2",
 "tokio",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-http"
version = "0.6.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adc82fd73de2a9722ac5da747f12383d2bfdb93591ee6c58486e0097890f05f2"
dependencies = [
 "bitflags 2.9.1",
 "bytes",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "iri-string",
 "pin-project-lite",
 "tower 0.5.2",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-layer"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "121c2a6cda46980bb0fcd1647ffaf6cd3fc79a013de288782836f6df9c48780e"

[[package]]
name = "tower-service"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8df9b6e13f2d32c91b9bd719c00d1958837bc7dec474d94952798cc8e69eeec3"

[[package]]
name = "tracing"
version = "0.1.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "784e0ac535deb450455cbfa28a6f0df145ea1bb7ae51b821cf5e7927fdcfbdd0"
dependencies = [
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "395ae124c09f9e6918a2310af6038fba074bcf474ac352496d5910dd59a2226d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "tracing-core"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e672c95779cf947c5311f83787af4fa8fffd12fb27e4993211a84bdfd9610f9c"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-opentelemetry"
version = "0.17.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbbe89715c1dbbb790059e2565353978564924ee85017b5fff365c872ff6721f"
dependencies = [
 "once_cell",
 "opentelemetry",
 "tracing",
 "tracing-core",
 "tracing-subscriber",
]

[[package]]
name = "tracing-subscriber"
version = "0.3.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2054a14f5307d601f88daf0553e1cbf472acc4f2c51afab632431cdcd72124d5"
dependencies = [
 "sharded-slab",
 "thread_local",
 "tracing-core",
]

[[package]]
name = "trait-set"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b79e2e9c9ab44c6d7c20d5976961b47e8f49ac199154daa514b77cd1ab536625"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "trees"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0de5f738ceab88e2491a94ddc33c3feeadfa95fedc60363ef110845df12f3878"

[[package]]
name = "try-lock"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e421abadd41a4225275504ea4d6566923418b7f05506fbc9c0fe86ba7396114b"

[[package]]
name = "tungstenite"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e3dac10fd62eaf6617d3a904ae222845979aec67c615d1c842b4002c7666fb9"
dependencies = [
 "byteorder",
 "bytes",
 "data-encoding",
 "http 0.2.12",
 "httparse",
 "log",
 "rand 0.8.5",
 "rustls 0.21.12",
 "sha1",
 "thiserror 1.0.69",
 "url 2.5.4",
 "utf-8",
 "webpki-roots 0.24.0",
]

[[package]]
name = "typenum"
version = "1.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1dccffe3ce07af9386bfd29e80c0ab1a8205a2fc34e4bcd40364df902cfa8f3f"

[[package]]
name = "ucd-trie"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2896d95c02a80c6d6a5d6e953d479f5ddf2dfdb6a244441010e373ac0fb88971"

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "unicase"
version = "2.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b844d17643ee918803943289730bec8aac480150456169e647ed0b576ba539"

[[package]]
name = "unicode-bidi"
version = "0.3.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c1cb5db39152898a79168971543b1cb5020dff7fe43c8dc468b0885f5e29df5"

[[package]]
name = "unicode-ident"
version = "1.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a5f39404a5da50712a4c1eecf25e90dd62b613502b7e925fd4e4d19b5c96512"

[[package]]
name = "unicode-normalization"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5033c97c4262335cded6d6fc3e5c18ab755e1a3dc96376350f3d8e9f009ad956"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-segmentation"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6ccf251212114b54433ec949fd6a7841275f9ada20dddd2f29e9ceea4501493"

[[package]]
name = "unicode-width"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dd6e30e90baa6f72411720665d41d89b9a3d039dc45b8faea1ddd07f617f6af"

[[package]]
name = "unicode-width"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fc81956842c57dac11422a97c3b8195a1ff727f06e85c84ed2e8aa277c9a0fd"

[[package]]
name = "unicode-xid"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc1c04c71510c7f702b52b7c350734c9ff1295c464a03335b00bb84fc54f853"

[[package]]
name = "universal-hash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc1de2c688dc15305988b563c3854064043356019f97a4b46276fe734c4f07ea"
dependencies = [
 "crypto-common",
 "subtle",
]

[[package]]
name = "unreachable"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "382810877fe448991dfc7f0dd6e3ae5d58088fd0ea5e35189655f84e6814fa56"
dependencies = [
 "void",
]

[[package]]
name = "unsafe-libyaml"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "673aac59facbab8a9007c7f6108d11f63b603f7cabff99fabf650fea5c32b861"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "unwrap_none"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "461d0c5956fcc728ecc03a3a961e4adc9a7975d86f6f8371389a289517c02ca9"

[[package]]
name = "uriparse"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0200d0fc04d809396c2ad43f3c95da3582a2556eba8d453c1087f4120ee352ff"
dependencies = [
 "fnv",
 "lazy_static",
]

[[package]]
name = "url"
version = "1.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd4e7c0d531266369519a4aa4f399d748bd37043b00bde1e4ff1f60a120b355a"
dependencies = [
 "idna 0.1.5",
 "matches",
 "percent-encoding 1.0.1",
]

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna 1.0.3",
 "percent-encoding 2.3.1",
]

[[package]]
name = "utf-8"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09cc8ee72d2a9becf2f2febe0205bbed8fc6615b7cb429ad062dc7b7ddd036a9"

[[package]]
name = "utf16_iter"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8232dd3cdaed5356e0f716d285e4b40b932ac434100fe9b7e0e8e935b9e6246"

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "utf8parse"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06abde3611657adf66d383f00b093d7faecc7fa57071cce2578660c9f1010821"

[[package]]
name = "valuable"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba73ea9cf16a25df0c8caa16c51acb937d5712a8429db78a3ee29d5dcacd3a65"

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "vec_extract_if_polyfill"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40c9cb5fb67c2692310b6eb3fce7dd4b6e4c9a75be4f2f46b27f0b2b7799759c"

[[package]]
name = "vec_map"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1bddf1187be692e79c5ffeab891132dfb0f236ed36a43c7ed39f1165ee20191"

[[package]]
name = "version_check"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b928f33d975fc6ad9f86c8f283853ad26bdd5b10b7f1542aa2fa15e2289105a"

[[package]]
name = "void"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a02e4885ed3bc0f2de90ea6dd45ebcbb66dacffe03547fadbb0eeae2770887d"

[[package]]
name = "wait-timeout"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ac3b126d3914f9849036f826e054cbabdc8519970b8998ddaf3b5bd3c65f11"
dependencies = [
 "libc",
]

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa7760aed19e106de2c7c0b581b509f2f25d3dacaf737cb82ac61bc6d760b0e"
dependencies = [
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.9.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cccddf32554fecc6acb585f82a32a72e28b48f8c4c1883ddfeeeaa96f7d8e519"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasi"
version = "0.13.3+wasi-0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26816d2e1a4a36a2940b96c5296ce403917633dff8f3440e9b236ed6f6bacad2"
dependencies = [
 "wit-bindgen-rt",
]

[[package]]
name = "wasm-bindgen"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1edc8929d7499fc4e8f0be2262a241556cfc54a0bea223790e71446f2aab1ef5"
dependencies = [
 "cfg-if 1.0.0",
 "once_cell",
 "rustversion",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f0a0651a5c2bc21487bde11ee802ccaf4c51935d0d3d42a6101f98161700bc6"
dependencies = [
 "bumpalo",
 "log",
 "proc-macro2",
 "quote",
 "syn 2.0.99",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "555d470ec0bc3bb57890405e5d4322cc9ea83cebb085523ced7be4144dac1e61"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "once_cell",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fe63fc6d09ed3792bd0897b314f53de8e16568c2b3f7982f468c0bf9bd0b407"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ae87ea40c9f689fc23f209965b6fb8a99ad69aeeb0231408be24920604395de"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a05d73b933a847d6cccdda8f838a22ff101ad9bf93e33684f39c1f5f0eece3d"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "web-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33b6dd2ef9186f1f2072e409e99cd22a975331a6b3591b12c764e0e55c60d5d2"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "web-time"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a6580f308b1fad9207618087a65c04e7a10bc77e02c8e84e9b00dd4b12fa0bb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki-root-certs"
version = "0.26.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09aed61f5e8d2c18344b3faa33a4c837855fe56642757754775548fee21386c4"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "webpki-roots"
version = "0.24.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b291546d5d9d1eab74f069c77749f2cb8504a12caa20f0f2de93ddbf6f411888"
dependencies = [
 "rustls-webpki 0.101.7",
]

[[package]]
name = "webpki-roots"
version = "0.25.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f20c57d8d7db6d3b86154206ae5d8fba62dd39573114de97c2cb0578251f8e1"

[[package]]
name = "webpki-roots"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8782dd5a41a24eed3a4f40b606249b3e236ca61adf1f25ea4d45c73de122b502"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "which"
version = "4.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87ba24419a2078cd2b0f2ede2691b6c66d8e47836da3b6db8265ebad47afbfc7"
dependencies = [
 "either",
 "home",
 "once_cell",
 "rustix 0.38.44",
]

[[package]]
name = "wide"
version = "0.7.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce5da8ecb62bcd8ec8b7ea19f69a51275e91299be594ea5cc6ef7819e16cd03"
dependencies = [
 "bytemuck",
 "safe_arch",
]

[[package]]
name = "winapi"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "167dc9d6949a9b857f3451275e911c3f44255842c1f7a76f33c55103a909087a"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-build"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d315eee3b34aca4797b2da6b13ed88266e6d612562a0c46390af8299fc699bc"

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf221c93e13a30d793f7645a0e7762c55d169dbb0a49671918a2319d289b10bb"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows-core"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33ab640c8d7e35bf8ba19b884ba838ceb4fba93a4e8c65a9059d08afcfc683d9"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-link"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dccfd733ce2b1753b03b6d3c65edf020262ea35e20ccdf3e288043e6dd620e3"

[[package]]
name = "windows-sys"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75283be5efb2831d37ea142365f009c02ec203cd29a3ebecbc093d52315b66d0"
dependencies = [
 "windows-targets 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.60.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2f500e4d28234f72040990ec9d39e3a6b950f9f22d3dba18416c35882612bcb"
dependencies = [
 "windows-targets 0.53.2",
]

[[package]]
name = "windows-targets"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm 0.52.6",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.53.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c66f69fcc9ce11da9966ddb31a40968cad001c5bedeb5c2b82ede4253ab48aef"
dependencies = [
 "windows_aarch64_gnullvm 0.53.0",
 "windows_aarch64_msvc 0.53.0",
 "windows_i686_gnu 0.53.0",
 "windows_i686_gnullvm 0.53.0",
 "windows_i686_msvc 0.53.0",
 "windows_x86_64_gnu 0.53.0",
 "windows_x86_64_gnullvm 0.53.0",
 "windows_x86_64_msvc 0.53.0",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b8d5f90ddd19cb4a147a5fa63ca848db3df085e25fee3cc10b39b6eebae764"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_aarch64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7651a1f62a11b8cbd5e0d42526e55f2c99886c77e007179efff86c2b137e66c"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1dc67659d35f387f5f6c479dc4e28f1d4bb90ddd1a5d3da2e5d97b42d6272c3"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce6ccbdedbf6d6354471319e781c0dfef054c81fbc7cf83f338a4296c0cae11"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_i686_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "581fee95406bb13382d2f65cd4a908ca7b1e4c2f1917f143ba16efe98a589b5d"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e55b5ac9ea33f2fc1716d1742db15574fd6fc8dadc51caab1c16a3d3b4190ba"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a6e035dd0599267ce1ee132e51c27dd29437f63325753051e71dd9e42406c57"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "windows_x86_64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271414315aff87387382ec3d271b52d7ae78726f5d44ac98b4f4030c91880486"

[[package]]
name = "winnow"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7f4ea97f6f78012141bcdb6a216b2609f0979ada50b20ca5b52dde2eac2bb1"
dependencies = [
 "memchr",
]

[[package]]
name = "winreg"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524e57b2c537c0f9b1e69f1965311ec12182b4122e45035b1508cd24d2adadb1"
dependencies = [
 "cfg-if 1.0.0",
 "windows-sys 0.48.0",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.33.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3268f3d866458b787f390cf61f4bbb563b922d091359f9608842999eaee3943c"
dependencies = [
 "bitflags 2.9.1",
]

[[package]]
name = "write16"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1890f4022759daae28ed4fe62859b1236caebfc61ede2f63ed4e695f3f6d936"

[[package]]
name = "writeable"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e9df38ee2d2c3c5948ea468a8406ff0db0b29ae1ffde1bcf20ef305bcc95c51"

[[package]]
name = "x509-parser"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0ecbeb7b67ce215e40e3cc7f2ff902f94a223acf44995934763467e7b1febc8"
dependencies = [
 "asn1-rs",
 "base64 0.13.1",
 "data-encoding",
 "der-parser",
 "lazy_static",
 "nom",
 "oid-registry",
 "rusticata-macros",
 "thiserror 1.0.69",
 "time",
]

[[package]]
name = "xattr"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d65cbf2f12c15564212d48f4e3dfb87923d25d611f2aed18f4cb23f0413d89e"
dependencies = [
 "libc",
 "rustix 1.0.0",
]

[[package]]
name = "yoke"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "120e6aef9aa629e3d4f52dc8cc43a015c7724194c97dfaf45180d2daf2b77f40"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2380878cad4ac9aac1e2435f3eb4020e8374b5f13c296cb75b4620ff8e229154"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
 "synstructure 0.13.1",
]

[[package]]
name = "zerocopy"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b9b4fd18abc82b8136838da5d50bae7bdea537c574d8dc1a34ed098d6c166f0"
dependencies = [
 "byteorder",
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa4f8080344d4671fb4e831a13ad1e68092748387dfc4f55e356242fae12ce3e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "zerofrom"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50cc42e0333e05660c3587f3bf9d0478688e15d870fab3346451ce7f8c9fbea5"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71e5d6e06ab090c67b5e44993ec16b72dcbaabc526db883a360057678b48502"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
 "synstructure 0.13.1",
]

[[package]]
name = "zeroize"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ced3678a2879b30306d323f4542626697a464a97c0a07c9aebf7ebca65cd4dde"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "zerovec"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa2b893d79df23bfb12d5461018d408ea19dfafe76c2c7ef6d4eba614f8ff079"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6eafa6dfb17584ea3e2bd6e76e0cc15ad7af12b09abdd1ca55961bed9b1063c6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.99",
]

[[package]]
name = "zstd"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e91ee311a569c327171651566e07972200e76fcfe2242a4fa446149a3881c08a"
dependencies = [
 "zstd-safe",
]

[[package]]
name = "zstd-safe"
version = "7.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3051792fbdc2e1e143244dc28c60f73d8470e93f3f9cbd0ead44da5ed802722"
dependencies = [
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.14+zstd.1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fb060d4926e4ac3a3ad15d864e99ceb5f343c6b34f5bd6d81ae6ed417311be5"
dependencies = [
 "cc",
 "pkg-config",
]

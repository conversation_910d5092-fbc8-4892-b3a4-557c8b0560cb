lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      '@codama/renderers-js':
        specifier: ^1.3.5
        version: 1.3.5(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)
      '@codama/renderers-rust':
        specifier: ^1.2.3
        version: 1.2.3(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)
      '@iarna/toml':
        specifier: ^2.2.5
        version: 2.2.5
      codama:
        specifier: ^1.3.4
        version: 1.3.4
      typescript:
        specifier: ^5.9.2
        version: 5.9.2
      zx:
        specifier: ^8.8.1
        version: 8.8.1

packages:

  '@codama/cli@1.3.2':
    resolution: {integrity: sha512-oAY9zbDEfhTkoqGi4J57AmMJp5f2yqyIFfnBhecBUagpa2NxFiYnAJE0hu+ekpM5N3n76THFAFXA9bG81wayJw==}
    hasBin: true

  '@codama/errors@1.3.3':
    resolution: {integrity: sha512-iyo5qEW/rgNTTtcZnGqahcnUtMHRRTlTzeTZo6SLpuNistbEn2itOssnklNZVClhXR/4Td0riHwGedP3AjwgJA==}
    hasBin: true

  '@codama/errors@1.3.4':
    resolution: {integrity: sha512-gEbfWkf5J1klGlLbrg5zIMYTncYO6SCsLRxKbDRrE1TmxRqt3cUovQyPhccGcNh/e/GisauIGjbsZTJaFNgXuw==}
    hasBin: true

  '@codama/node-types@1.3.3':
    resolution: {integrity: sha512-41GdFy/OPRemXTAmptDSi/wDXHPimy40mx9v0z2EdIMNYEyKAYZDcvr2jzSei8meeCV9j4PgKF5snwBQK7HaSg==}

  '@codama/node-types@1.3.4':
    resolution: {integrity: sha512-WOKU9v019gfX58y+I+hadokckvYLEZKvcsXpf578S+B+sV9YBCOhIjlzDDONcfe9iGUw49M6VpyWkp+EenasLg==}

  '@codama/nodes@1.3.3':
    resolution: {integrity: sha512-CgxGfH6ndcZpvf+RfVwSyIOyq8cNejbqY9TRr4SqVMIEbE1Wpqx+GPrEGRKtwHgB8KqsWCz7Pve8BCGhsPkr2g==}

  '@codama/nodes@1.3.4':
    resolution: {integrity: sha512-Q3eS/kMqiozZzlpY/otVfQQ9M3pRdCS0D1dlB3TyuHWBAJiAGPfjRT21KQ4M8xub6eTWJY7PwT7sykPqS/fQPg==}

  '@codama/renderers-core@1.1.0':
    resolution: {integrity: sha512-OscEiIv4nXiiqb2I9PWDI0Aw8bM6Qdtb9oqbuFb0XXt8hJTeLoNk+AN8ZZFtJMtiy+SizJjUsbX2KFE2HsVhdg==}

  '@codama/renderers-js@1.3.5':
    resolution: {integrity: sha512-c23KBVOqmNd02pzxSTGxBUKVnC6LOZOK2KPeYCb3EsmD5E4YSEFAMt2bI66BsbzuisKxLya9dop7UG5wRGfIIg==}

  '@codama/renderers-rust@1.2.3':
    resolution: {integrity: sha512-YHD55YQ+DitWDe1uEsAubQ7sIzKF2x420RA8/FSGplx4pvarU0i1REjzbKZEydVTBzWUPHGcvUVMXINuPIpEmg==}

  '@codama/validators@1.3.4':
    resolution: {integrity: sha512-VlmR5lLEm3monBtQYTz4A/pYHsqFlkew6zlli1+P/IKcnIfH5Px6XJhTVyP7hcD1eHaPKLbWiLidQAxLmRdtiw==}

  '@codama/visitors-core@1.3.3':
    resolution: {integrity: sha512-Kuz2we5iDhq0Y9bPwEjEGGSueBPJkLxoDkJ+Z3NuHlqo/k2aHvDNl9NaoOOUPwNzPVbntfpJW9Ga3pP9oc/PQQ==}

  '@codama/visitors-core@1.3.4':
    resolution: {integrity: sha512-TCP1ncA0ErBvv5q8BKCRvv98qHh9on8MOiPhdToJ/w50aNm0y5Y2mHbUWqk5vle8k3V9fuo7uf1aAOaqA6G4Vg==}

  '@codama/visitors@1.3.4':
    resolution: {integrity: sha512-95asorZEjGTmCIcDlZL0jdPxVwelU4paa5y76THM90gox6C4qZFimc+U1o8PXg2phJlyEi034NQpX+k05dh86g==}

  '@iarna/toml@2.2.5':
    resolution: {integrity: sha512-trnsAYxU3xnS1gPHPyU961coFyLkh4gAD/0zQ5mymY4yOZ+CYvsPqUbOFSw0aDM4y0tV7tiFxL/1XfXPNC6IPg==}

  '@solana/codecs-core@3.0.1':
    resolution: {integrity: sha512-7U12QJX6VZeq03r0l0SZnGPk+YHbyU9lDjEZpjiSHdWbixy8inoYTsqV523Soy41SnGwvVsfiJf7SUB5kvgGaQ==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/codecs-core@3.0.2':
    resolution: {integrity: sha512-vpy8ySWgPF8+APwpeEr4dQbU/EyHjisd/TywIw3rymguWeZ9/wcThS0WDRi08Z44W6InCIbtI08RdtuHQZoSag==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/codecs-numbers@3.0.1':
    resolution: {integrity: sha512-HdXioWMVc0Ih9N7vuEF11pMwQWHqSF/TQrCDKljr981QQ5q/pBleUZB3hM43zP/ndnTasIxqAuLXIypricAGqg==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/codecs-numbers@3.0.2':
    resolution: {integrity: sha512-Yr9kWo2BF4Wdu9mhceUQ3ksA6ZTi2CvQr8GWkRh9DQjvmrdqE2vwxwWWcmnzGDzwbta5o4tAr8JjtAtmwpiLEg==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/codecs-strings@3.0.1':
    resolution: {integrity: sha512-UxaD1Xdxe35Y2B0HnSHe48ok8ew+tyrVI1w65eHj0NzGb9FxLx/KuuEg/b/qXQRUB75JQjDo8IBh5rWZ7rl5qQ==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      fastestsmallesttextencoderdecoder: ^1.0.22
      typescript: '>=5.3.3'

  '@solana/codecs-strings@3.0.2':
    resolution: {integrity: sha512-V7Ivy3TjdrLL4E3wT2d32MpGYQFWM1UKnevLXPib/Aqws4nP/bGMHq6gSIJ4b9HrkFfrlEgSjusOyEJbRBBFag==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      fastestsmallesttextencoderdecoder: ^1.0.22
      typescript: '>=5.3.3'

  '@solana/errors@3.0.1':
    resolution: {integrity: sha512-/EhRoJLrO03wj+3/miA6YkBnGjAS12mvilWkQ4LBv/hVqhUF9V8JXpSMnD40hoaMraE23V9IaHNHcfoJmldjwA==}
    engines: {node: '>=20.18.0'}
    hasBin: true
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/errors@3.0.2':
    resolution: {integrity: sha512-0B4y9JUsX8/DzflhdSXSAF+UPUKyo1R1rrQ/ShS/8ApCOIWIFqZlve0TPatuTOGGNBememoukPOvDVtFy0ZYpg==}
    engines: {node: '>=20.18.0'}
    hasBin: true
    peerDependencies:
      typescript: '>=5.3.3'

  a-sync-waterfall@1.0.1:
    resolution: {integrity: sha512-RYTOHHdWipFUliRFMCS4X2Yn2X8M87V/OpSqWzKKOGhzqyUxzyVmhHDH9sAvG+ZuQf/TAOFsLCpMw09I1ufUnA==}

  asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}

  chalk@5.6.0:
    resolution: {integrity: sha512-46QrSQFyVSEyYAgQ22hQ+zDa60YHA4fBstHmtSApj1Y5vKtG27fWowW03jCk5KcbXEWPZUIR894aARCA/G1kfQ==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  codama@1.3.4:
    resolution: {integrity: sha512-nAK1i2w8z3dGwcUDw0yhaX2o8NuIv98haWlUqBcdn+sFGZYqL3xvG0p/0Wm7KXB0F6FK0txByTg7pBQJ0SxyPQ==}
    hasBin: true

  commander@14.0.0:
    resolution: {integrity: sha512-2uM9rYjPvyq39NwLRqaiLtWHyDC1FvryJDa2ATTVims5YAS4PupsEQsDvP14FqhFr0P49CYDugi59xaxJlTXRA==}
    engines: {node: '>=20'}

  commander@5.1.0:
    resolution: {integrity: sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==}
    engines: {node: '>= 6'}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  fastestsmallesttextencoderdecoder@1.0.22:
    resolution: {integrity: sha512-Pb8d48e+oIuY4MaM64Cd7OW1gt4nxCHs7/ddPPZ/Ic3sg8yVGM7O9wDvZ7us6ScaUupzM+pfBolwtYhN1IxBIw==}

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  json-stable-stringify@1.3.0:
    resolution: {integrity: sha512-qtYiSSFlwot9XHtF9bD9c7rwKjr+RecWT//ZnPvSmEjpV5mmPOCN4j8UjY5hbjNkOwZ/jQv3J6R1/pL7RwgMsg==}
    engines: {node: '>= 0.4'}

  jsonify@0.0.1:
    resolution: {integrity: sha512-2/Ki0GcmuqSrgFyelQq9M05y7PS0mEwuIzrf3f1fPqkVDVRvZrPZtVSMHxdgo8Aq0sxAOb/cr2aqqA3LeWHVPg==}

  kleur@3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  nunjucks@3.2.4:
    resolution: {integrity: sha512-26XRV6BhkgK0VOxfbU5cQI+ICFUtMLixv1noZn1tGU38kQH5A5nmmbk/O45xdyBhD1esk47nKrY0mvQpZIhRjQ==}
    engines: {node: '>= 6.9.0'}
    hasBin: true
    peerDependencies:
      chokidar: ^3.3.0
    peerDependenciesMeta:
      chokidar:
        optional: true

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  prettier@3.6.2:
    resolution: {integrity: sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==}
    engines: {node: '>=14'}
    hasBin: true

  prompts@2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  sisteransi@1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}

  typescript@5.9.2:
    resolution: {integrity: sha512-CWBzXQrc/qOkhidw1OzBTQuYRbfyxDXJMVJ1XNwUHGROVmuaeiEm3OslpZ1RV96d7SKKjZKrSJu3+t/xlw3R9A==}
    engines: {node: '>=14.17'}
    hasBin: true

  zx@8.8.1:
    resolution: {integrity: sha512-qvsKBnvWHstHKCluKPlEgI/D3+mdiQyMoSSeFR8IX/aXzWIas5A297KxKgPJhuPXdrR6ma0Jzx43+GQ/8sqbrw==}
    engines: {node: '>= 12.17.0'}
    hasBin: true

snapshots:

  '@codama/cli@1.3.2':
    dependencies:
      '@codama/nodes': 1.3.4
      '@codama/visitors': 1.3.4
      '@codama/visitors-core': 1.3.4
      commander: 14.0.0
      picocolors: 1.1.1
      prompts: 2.4.2

  '@codama/errors@1.3.3':
    dependencies:
      '@codama/node-types': 1.3.3
      chalk: 5.6.0
      commander: 14.0.0

  '@codama/errors@1.3.4':
    dependencies:
      '@codama/node-types': 1.3.4
      chalk: 5.6.0
      commander: 14.0.0

  '@codama/node-types@1.3.3': {}

  '@codama/node-types@1.3.4': {}

  '@codama/nodes@1.3.3':
    dependencies:
      '@codama/errors': 1.3.3
      '@codama/node-types': 1.3.3

  '@codama/nodes@1.3.4':
    dependencies:
      '@codama/errors': 1.3.4
      '@codama/node-types': 1.3.4

  '@codama/renderers-core@1.1.0':
    dependencies:
      '@codama/errors': 1.3.4
      '@codama/nodes': 1.3.4
      '@codama/visitors-core': 1.3.4

  '@codama/renderers-js@1.3.5(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)':
    dependencies:
      '@codama/errors': 1.3.4
      '@codama/nodes': 1.3.4
      '@codama/renderers-core': 1.1.0
      '@codama/visitors-core': 1.3.4
      '@solana/codecs-strings': 3.0.2(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)
      nunjucks: 3.2.4
      prettier: 3.6.2
    transitivePeerDependencies:
      - chokidar
      - fastestsmallesttextencoderdecoder
      - typescript

  '@codama/renderers-rust@1.2.3(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)':
    dependencies:
      '@codama/errors': 1.3.4
      '@codama/nodes': 1.3.4
      '@codama/renderers-core': 1.1.0
      '@codama/visitors-core': 1.3.4
      '@solana/codecs-strings': 3.0.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)
      nunjucks: 3.2.4
    transitivePeerDependencies:
      - chokidar
      - fastestsmallesttextencoderdecoder
      - typescript

  '@codama/validators@1.3.4':
    dependencies:
      '@codama/errors': 1.3.4
      '@codama/nodes': 1.3.4
      '@codama/visitors-core': 1.3.4

  '@codama/visitors-core@1.3.3':
    dependencies:
      '@codama/errors': 1.3.3
      '@codama/nodes': 1.3.3
      json-stable-stringify: 1.3.0

  '@codama/visitors-core@1.3.4':
    dependencies:
      '@codama/errors': 1.3.4
      '@codama/nodes': 1.3.4
      json-stable-stringify: 1.3.0

  '@codama/visitors@1.3.4':
    dependencies:
      '@codama/errors': 1.3.4
      '@codama/nodes': 1.3.4
      '@codama/visitors-core': 1.3.4

  '@iarna/toml@2.2.5': {}

  '@solana/codecs-core@3.0.1(typescript@5.9.2)':
    dependencies:
      '@solana/errors': 3.0.1(typescript@5.9.2)
      typescript: 5.9.2

  '@solana/codecs-core@3.0.2(typescript@5.9.2)':
    dependencies:
      '@solana/errors': 3.0.2(typescript@5.9.2)
      typescript: 5.9.2

  '@solana/codecs-numbers@3.0.1(typescript@5.9.2)':
    dependencies:
      '@solana/codecs-core': 3.0.1(typescript@5.9.2)
      '@solana/errors': 3.0.1(typescript@5.9.2)
      typescript: 5.9.2

  '@solana/codecs-numbers@3.0.2(typescript@5.9.2)':
    dependencies:
      '@solana/codecs-core': 3.0.2(typescript@5.9.2)
      '@solana/errors': 3.0.2(typescript@5.9.2)
      typescript: 5.9.2

  '@solana/codecs-strings@3.0.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)':
    dependencies:
      '@solana/codecs-core': 3.0.1(typescript@5.9.2)
      '@solana/codecs-numbers': 3.0.1(typescript@5.9.2)
      '@solana/errors': 3.0.1(typescript@5.9.2)
      fastestsmallesttextencoderdecoder: 1.0.22
      typescript: 5.9.2

  '@solana/codecs-strings@3.0.2(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.9.2)':
    dependencies:
      '@solana/codecs-core': 3.0.2(typescript@5.9.2)
      '@solana/codecs-numbers': 3.0.2(typescript@5.9.2)
      '@solana/errors': 3.0.2(typescript@5.9.2)
      fastestsmallesttextencoderdecoder: 1.0.22
      typescript: 5.9.2

  '@solana/errors@3.0.1(typescript@5.9.2)':
    dependencies:
      chalk: 5.6.0
      commander: 14.0.0
      typescript: 5.9.2

  '@solana/errors@3.0.2(typescript@5.9.2)':
    dependencies:
      chalk: 5.6.0
      commander: 14.0.0
      typescript: 5.9.2

  a-sync-waterfall@1.0.1: {}

  asap@2.0.6: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  chalk@5.6.0: {}

  codama@1.3.4:
    dependencies:
      '@codama/cli': 1.3.2
      '@codama/errors': 1.3.4
      '@codama/nodes': 1.3.4
      '@codama/validators': 1.3.4
      '@codama/visitors': 1.3.4

  commander@14.0.0: {}

  commander@5.1.0: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  fastestsmallesttextencoderdecoder@1.0.22: {}

  function-bind@1.1.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  gopd@1.2.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-symbols@1.1.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  isarray@2.0.5: {}

  json-stable-stringify@1.3.0:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      isarray: 2.0.5
      jsonify: 0.0.1
      object-keys: 1.1.1

  jsonify@0.0.1: {}

  kleur@3.0.3: {}

  math-intrinsics@1.1.0: {}

  nunjucks@3.2.4:
    dependencies:
      a-sync-waterfall: 1.0.1
      asap: 2.0.6
      commander: 5.1.0

  object-keys@1.1.1: {}

  picocolors@1.1.1: {}

  prettier@3.6.2: {}

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  sisteransi@1.0.5: {}

  typescript@5.9.2: {}

  zx@8.8.1: {}

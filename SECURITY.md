# Security Policy

## Reporting security problems

**DO NOT CREATE A GITHUB ISSUE** to report a security problem.

Instead please use this [Report a Vulnerability](https://github.com/solana-program/token-2022/security/advisories/new) link.
Provide a helpful title and detailed description of the problem.

If you haven't done so already, please **enable two-factor auth** in your GitHub account.

Expect a response as fast as possible in the advisory, typically within 72 hours.

--

If you do not receive a response in the advisory, send an email to
<<EMAIL>> with the full URL of the advisory you have created. DO NOT
include attachments or provide detail sufficient for exploitation regarding the
security issue in this email. **Only provide such details in the advisory**.

If you do not receive a response from <<EMAIL>> please followup with
the team directly. You can do this in one of the `#Dev Tooling` channels of the
[Solana Tech discord server](https://solana.com/discord), by pinging the admins
in the channel and referencing the fact that you submitted a security problem.

## Security Bug Bounties

The Solana Foundation offer bounties for critical security issues. Please
see the [Agave Security Bug
Bounties](https://github.com/anza-xyz/agave/security/policy#security-bug-bounties)
for details on classes of bugs and payment amounts.

## Scope

Only the `spl-token-2022` program is included in the bounty scope, at [program](https://github.com/solana-program/token-2022/tree/master/program).

If you discover a critical security issue in an out-of-scope component, your finding
may still be valuable.

[package]
name = "spl-token-confidential-transfer-ciphertext-arithmetic"
version = "0.4.0"
description = "Solana Program Library Confidential Transfer Ciphertext Arithmetic"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[dependencies]
base64 = "0.22.1"
bytemuck = "1.23.1"
solana-curve25519 = "2.3.4"
solana-zk-sdk = "4.0.0"

[dev-dependencies]
spl-token-confidential-transfer-proof-generation = { version = "0.5.0", path = "../proof-generation" }
curve25519-dalek = "4.1.3"

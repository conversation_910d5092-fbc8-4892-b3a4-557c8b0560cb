use {solana_zk_sdk::zk_elgamal_proof_program::errors::ProofGenerationError, thiserror::Error};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bug, Eq, PartialEq)]
pub enum TokenProofGenerationError {
    #[error("inner proof generation failed")]
    ProofGeneration(#[from] ProofGenerationError),
    #[error("not enough funds in account")]
    NotEnoughFunds,
    #[error("illegal amount bit length")]
    IllegalAmountBitLength,
    #[error("fee calculation failed")]
    FeeCalculation,
    #[error("ciphertext extraction failed")]
    CiphertextExtraction,
}

[package]
name = "spl-token-confidential-transfer-proof-generation"
version = "0.5.0"
description = "Solana Program Library Confidential Transfer Proof Generation"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[dependencies]
curve25519-dalek = "4.1.3"
solana-zk-sdk = "4.0.0"
thiserror = "2.0.12"

[lints]
workspace = true

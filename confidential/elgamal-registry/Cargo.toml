[package]
name = "spl-elgamal-registry"
version = "0.3.0"
description = "Solana ElGamal Registry Program"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[features]
no-entrypoint = []
test-sbf = []

[dependencies]
bytemuck = { version = "1.23.1", features = ["derive"] }
solana-account-info = "2.3.0"
solana-cpi = "2.2.1"
solana-instruction = "2.2.1"
solana-msg = "2.2.1"
solana-program-entrypoint = "2.3.0"
solana-program-error = "2.2.1"
solana-pubkey = { version = "2.2.1", features = ["curve25519"] }
solana-rent = "2.2.1"
solana-sdk-ids = "2.2.1"
solana-security-txt = "1.1.1"
solana-system-interface = { version = "1.0.0", features = ["bincode"] }
solana-sysvar = { version = "2.3.0", features = ["bincode"] }
solana-zk-sdk = "2.3.4"
spl-pod = "0.5.1"
spl-token-confidential-transfer-proof-extraction = { version = "0.4.1" }

[lib]
crate-type = ["cdylib", "lib"]

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[package.metadata.solana]
program-id = "regVYJW7tcT8zipN5YiBvHsvR5jXW1uLFxaHSbugABg"

[lints]
workspace = true

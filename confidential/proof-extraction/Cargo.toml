[package]
name = "spl-token-confidential-transfer-proof-extraction"
version = "0.5.0"
description = "Solana Program Library Confidential Transfer Proof Extraction"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[dependencies]
bytemuck = "1.23.1"
solana-account-info = "3.0.0"
solana-curve25519 = "2.3.4"
solana-instruction = "3.0.0"
solana-instructions-sysvar = "3.0.0"
solana-msg = "3.0.0"
solana-program-error = "3.0.0"
solana-pubkey = "3.0.0"
solana-sdk-ids = "3.0.0"
solana-zk-sdk = "4.0.0"
spl-pod = "0.7.0"
thiserror = "2.0.12"

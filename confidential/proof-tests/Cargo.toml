[package]
name = "spl-token-confidential-transfer-proof-test"
version = "0.0.1"
publish = false
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[dev-dependencies]
curve25519-dalek = "4.1.3"
solana-zk-sdk = "4.0.0"
thiserror = "2.0.12"
spl-token-confidential-transfer-proof-extraction = { version = "0.5.0", path = "../proof-extraction" }
spl-token-confidential-transfer-proof-generation = { version = "0.5.0", path = "../proof-generation" }

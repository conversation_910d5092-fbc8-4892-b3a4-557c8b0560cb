name: Publish Rust Crate

on:
  workflow_dispatch:
    inputs:
      package-path:
        description: Path to directory with package to release
        required: true
        default: 'clients/cli'
        type: choice
        options:
          - clients/cli
          - clients/rust-legacy
          - confidential-transfer/ciphertext-arithmetic
          - confidential-transfer/elgamal-registry
          - confidential-transfer/proof-extraction
          - confidential-transfer/proof-generation
          - confidential-transfer/proof-tests
          - interface
          - program
      level:
        description: Level
        required: true
        default: patch
        type: choice
        options:
          - patch
          - minor
          - major
          - rc
          - beta
          - alpha
          - release
          - version
      version:
        description: Version (used with level "version")
        required: false
        type: string
      dry-run:
        description: Dry run
        required: true
        default: true
        type: boolean
      create-release:
        description: Create a GitHub release
        required: true
        type: boolean
        default: true

jobs:
  set_env:
    name: Set variables to be used in strategy definitions
    runs-on: ubuntu-latest
    outputs:
      RUST_TOOLCHAIN_NIGHTLY: ${{ steps.compute.outputs.RUST_TOOLCHAIN_NIGHTLY }}
      SOLANA_CLI_VERSION: ${{ steps.compute.outputs.SOLANA_CLI_VERSION }}
      TARGET: ${{ steps.compute.outputs.TARGET }}
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Compute variables
        id: compute
        shell: bash
        run: |
          echo "RUST_TOOLCHAIN_NIGHTLY=$(make rust-toolchain-nightly)" >> "$GITHUB_OUTPUT"
          echo "SOLANA_CLI_VERSION=$(make solana-cli-version)" >> "$GITHUB_OUTPUT"
          TARGET=$(echo ${{ inputs.package-path }} | sed 's#/#-#')
          echo "TARGET=$TARGET" >> "$GITHUB_OUTPUT"

  main:
    needs: set_env
    uses: solana-program/actions/.github/workflows/publish-rust.yml@main
    with:
      sbpf-program-packages: "program confidential-elgamal-registry"
      solana-cli-version: ${{ needs.set_env.outputs.SOLANA_CLI_VERSION }}
      clippy-toolchain: ${{ needs.set_env.outputs.RUST_TOOLCHAIN_NIGHTLY }}
      rustfmt-toolchain: ${{ needs.set_env.outputs.RUST_TOOLCHAIN_NIGHTLY }}
      target: ${{ needs.set_env.outputs.TARGET }}
      package-path: ${{ inputs.package-path }}
      level: ${{ inputs.level }}
      version: ${{ inputs.version }}
      create-release: ${{ inputs.create-release }}
      dry-run: ${{ inputs.dry-run }}
    secrets: inherit

name: Main

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  JS_PACKAGES: "['clients-js', 'clients-js-legacy']"
  SBPF_PROGRAM_PACKAGES: "['confidential-elgamal-registry', 'program']"
  RUST_PACKAGES: "['clients-cli', 'clients-rust-legacy', 'interface', 'program', 'confidential-ciphertext-arithmetic', 'confidential-elgamal-registry', 'confidential-proof-extraction', 'confidential-proof-generation', 'confidential-proof-tests']"
  WASM_PACKAGES: "['interface', 'program']"

jobs:
  set_env:
    name: Set variables to be used in strategy definitions in reusable workflow
    runs-on: ubuntu-latest
    outputs:
      JS_PACKAGES: ${{ steps.compute.outputs.JS_PACKAGES }}
      SBPF_PROGRAM_PACKAGES: ${{ steps.compute.outputs.SBPF_PROGRAM_PACKAGES }}
      RUST_PACKAGES: ${{ steps.compute.outputs.RUST_PACKAGES }}
      WASM_PACKAGES: ${{ steps.compute.outputs.WASM_PACKAGES }}
      RUST_TOOLCHAIN_NIGHTLY: ${{ steps.compute.outputs.RUST_TOOLCHAIN_NIGHTLY }}
      SOLANA_CLI_VERSION: ${{ steps.compute.outputs.SOLANA_CLI_VERSION }}
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Compute variables
        id: compute
        shell: bash
        run: |
          echo "JS_PACKAGES=${{ env.JS_PACKAGES }}" >> $GITHUB_OUTPUT
          echo "SBPF_PROGRAM_PACKAGES=${{ env.SBPF_PROGRAM_PACKAGES }}" >> $GITHUB_OUTPUT
          echo "RUST_PACKAGES=${{ env.RUST_PACKAGES }}" >> $GITHUB_OUTPUT
          echo "WASM_PACKAGES=${{ env.WASM_PACKAGES }}" >> $GITHUB_OUTPUT
          echo "RUST_TOOLCHAIN_NIGHTLY=$(make rust-toolchain-nightly)" >> "$GITHUB_OUTPUT"
          echo "SOLANA_CLI_VERSION=$(make solana-cli-version)" >> "$GITHUB_OUTPUT"

  main:
    needs: set_env
    uses: solana-program/actions/.github/workflows/main.yml@main
    with:
      js-packages: ${{ needs.set_env.outputs.JS_PACKAGES }}
      sbpf-program-packages: ${{ needs.set_env.outputs.SBPF_PROGRAM_PACKAGES }}
      rust-packages: ${{ needs.set_env.outputs.RUST_PACKAGES }}
      wasm-packages: ${{ needs.set_env.outputs.WASM_PACKAGES }}
      rustfmt-toolchain: ${{ needs.set_env.outputs.RUST_TOOLCHAIN_NIGHTLY }}
      clippy-toolchain: ${{ needs.set_env.outputs.RUST_TOOLCHAIN_NIGHTLY }}
      solana-cli-version: ${{ needs.set_env.outputs.SOLANA_CLI_VERSION }}

[package]
name = "spl-token-2022"
version = "9.0.0"
description = "Solana Program Library Token 2022"
documentation = "https://docs.rs/spl-token-2022"
readme = "README.md"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[features]
no-entrypoint = []
test-sbf = []
serde-traits = ["dep:serde", "dep:serde_with", "dep:base64", "spl-pod/serde-traits", "spl-token-2022-interface/serde"]
default = ["zk-ops"]
# Remove this feature once the underlying syscalls are released on all networks
zk-ops = []

[dependencies]
arrayref = "0.3.9"
bytemuck = { version = "1.23.1", features = ["derive"] }
num-derive = "0.4"
num-traits = "0.2"
num_enum = "0.7.4"
solana-account-info = "2.3.0"
solana-clock = "2.2.1"
solana-cpi = "2.2.1"
solana-instruction = "2.2.1"
solana-msg = "2.2.1"
solana-program-entrypoint = "2.3.0"
solana-program-error = "2.2.1"
solana-program-memory = "2.3.1"
solana-program-option = "2.2.1"
solana-program-pack = "2.2.1"
solana-pubkey = "2.2.1"
solana-rent = "2.2.1"
solana-sdk-ids = "2.2.1"
solana-security-txt = "1.1.1"
solana-sysvar = "2.3.0"
solana-system-interface = "1.0.0"
solana-zk-sdk = "2.3.4"
spl-elgamal-registry = { version = "0.3.0", path = "../confidential/elgamal-registry", features = ["no-entrypoint"] }
spl-memo = { version = "6.0", features = ["no-entrypoint"] }
spl-token-2022-interface = { version = "1.0" }
spl-token-confidential-transfer-ciphertext-arithmetic = { version = "0.3.1" }
spl-token-confidential-transfer-proof-extraction = { version = "0.4.1" }
spl-token-group-interface = { version = "0.6.0" }
spl-token-metadata-interface = { version = "0.7.0" }
spl-transfer-hook-interface = { version = "0.10.0" }
spl-pod = { version = "0.5.1" }
thiserror = "2.0"
serde = { version = "1.0.219", optional = true }
serde_with = { version = "3.14.0", optional = true }
base64 = { version = "0.22.1", optional = true }

[target.'cfg(not(target_os = "solana"))'.dependencies]
spl-token-confidential-transfer-proof-generation = { version = "0.4.1" }

[dev-dependencies]
lazy_static = "1.5.0"
proptest = "1.7"
serial_test = "3.2.0"
solana-account = "2.2.1"
solana-hash = "2.2.1"
solana-keypair = "2.2.1"
solana-program-test = "2.3.4"
solana-signer = "2.2.1"
solana-transaction = "2.2.1"
solana-transaction-error = "2.2.1"
spl-tlv-account-resolution = { version = "0.10.0" }
serde_json = "1.0.142"
test-case = "3.3.1"

[lib]
crate-type = ["cdylib", "lib"]

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[lints]
workspace = true

[package.metadata.solana]
program-id = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"

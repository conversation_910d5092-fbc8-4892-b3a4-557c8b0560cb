#[cfg(feature = "serde")]
use serde::{Deserialize, Serialize};
use {
    crate::extension::{Extension, ExtensionType},
    bytemuck::{Pod, Zeroable},
    spl_pod::optional_keys::OptionalNonZeroPubkey,
};

/// Instructions for the `GroupMemberPointer` extension
pub mod instruction;

/// Group member pointer extension data for mints.
#[repr(C)]
#[cfg_attr(feature = "serde", derive(Serialize, Deserialize))]
#[cfg_attr(feature = "serde", serde(rename_all = "camelCase"))]
#[derive(Clone, Co<PERSON>, Debug, De<PERSON>ult, PartialEq, Pod, Zeroable)]
pub struct GroupMemberPointer {
    /// Authority that can set the member address
    pub authority: OptionalNonZeroPubkey,
    /// Account address that holds the member
    pub member_address: OptionalNonZeroPubkey,
}

impl Extension for GroupMemberPointer {
    const TYPE: ExtensionType = ExtensionType::GroupMemberPointer;
}

#[cfg(feature = "serde")]
use serde::{Deserialize, Serialize};
use {
    crate::extension::{Extension, ExtensionType},
    bytemuck::{Pod, Zeroable},
};

/// Indicates that the Account owner authority cannot be changed
#[cfg_attr(feature = "serde", derive(Serialize, Deserialize))]
#[cfg_attr(feature = "serde", serde(rename_all = "camelCase"))]
#[derive(<PERSON><PERSON>, Co<PERSON>, Debug, <PERSON><PERSON><PERSON>, PartialEq, Pod, Zeroable)]
#[repr(transparent)]
pub struct ImmutableOwner;

impl Extension for ImmutableOwner {
    const TYPE: ExtensionType = ExtensionType::ImmutableOwner;
}

#[cfg(feature = "serde")]
use serde::{Deserialize, Serialize};
use {
    crate::extension::{Extension, ExtensionType},
    bytemuck::{Pod, Zeroable},
};

/// Indicates that the tokens from this mint can't be transferred
#[cfg_attr(feature = "serde", derive(Serialize, Deserialize))]
#[cfg_attr(feature = "serde", serde(rename_all = "camelCase"))]
#[derive(<PERSON><PERSON>, Co<PERSON>, Debug, De<PERSON>ult, PartialEq, Pod, Zeroable)]
#[repr(transparent)]
pub struct NonTransferable;

/// Indicates that the tokens from this account belong to a non-transferable
/// mint
#[cfg_attr(feature = "serde", derive(Serialize, Deserialize))]
#[cfg_attr(feature = "serde", serde(rename_all = "camelCase"))]
#[derive(Clone, Copy, Debug, Default, PartialEq, Pod, Zeroable)]
#[repr(transparent)]
pub struct NonTransferableAccount;

impl Extension for NonTransferable {
    const TYPE: ExtensionType = ExtensionType::NonTransferable;
}

impl Extension for NonTransferableAccount {
    const TYPE: ExtensionType = ExtensionType::NonTransferableAccount;
}

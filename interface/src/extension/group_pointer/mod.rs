#[cfg(feature = "serde")]
use serde::{Deserialize, Serialize};
use {
    crate::extension::{Extension, ExtensionType},
    bytemuck::{Pod, Zeroable},
    spl_pod::optional_keys::OptionalNonZeroPubkey,
};

/// Instructions for the `GroupPointer` extension
pub mod instruction;

/// Group pointer extension data for mints.
#[repr(C)]
#[cfg_attr(feature = "serde", derive(Serialize, Deserialize))]
#[cfg_attr(feature = "serde", serde(rename_all = "camelCase"))]
#[derive(Clone, Copy, Debug, De<PERSON>ult, PartialEq, Pod, Zeroable)]
pub struct GroupPointer {
    /// Authority that can set the group address
    pub authority: OptionalNonZeroPubkey,
    /// Account address that holds the group
    pub group_address: OptionalNonZeroPubkey,
}

impl Extension for GroupPointer {
    const TYPE: ExtensionType = ExtensionType::GroupPointer;
}

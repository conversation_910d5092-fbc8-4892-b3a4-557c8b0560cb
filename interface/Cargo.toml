[package]
name = "spl-token-2022-interface"
version = "2.0.0"
description = "Solana Program Library Token 2022 Interface"
documentation = "https://docs.rs/spl-token-2022-interface"
readme = "README.md"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[features]
serde = ["dep:serde", "dep:serde_with", "dep:base64", "spl-pod/serde-traits", "solana-pubkey/serde"]

[dependencies]
arrayref = "0.3.9"
bytemuck = { version = "1.23.1", features = ["derive"] }
num-derive = "0.4"
num-traits = "0.2"
num_enum = "0.7.4"
solana-account-info = "3.0.0"
solana-instruction = "3.0.0"
solana-program-error = "3.0.0"
solana-program-option = "3.0.0"
solana-program-pack = "3.0.0"
solana-pubkey = "3.0.0"
solana-sdk-ids = "3.0.0"
solana-zk-sdk = "4.0.0"
spl-token-confidential-transfer-proof-extraction = { version = "0.5.0", path = "../confidential/proof-extraction" }
spl-token-group-interface = { version = "0.7.1" }
spl-token-metadata-interface = { version = "0.8.0" }
spl-type-length-value = { version = "0.9.0" }
spl-pod = { version = "0.7.0" }
thiserror = "2.0"
serde = { version = "1.0.219", optional = true }
serde_with = { version = "3.14.0", optional = true }
base64 = { version = "0.22.1", optional = true }

[target.'cfg(not(target_os = "solana"))'.dependencies]
spl-token-confidential-transfer-proof-generation = { version = "0.5.0", path = "../confidential/proof-generation" }

[dev-dependencies]
proptest = "1.7"
solana-pubkey = { version = "3.0.0", features = ["curve25519"] }
spl-token-interface = { version = "2.0" }
spl-token-2022-interface = { path = ".", features = ["serde"] }
serde_json = "1.0.141"

[lib]
crate-type = ["lib"]

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[lints]
workspace = true

[package.metadata.solana]
program-id = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
